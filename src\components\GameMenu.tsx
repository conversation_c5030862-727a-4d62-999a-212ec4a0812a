import React, { useState, useEffect } from "react";
import { useAudio } from "@/hooks/useAudio";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import ActionButton from "@/components/ui/ActionButton";
import { LogOut, Menu, X, Eye } from "lucide-react";
import { cn } from "@/lib/utils";
import GameExitConfirmDialog from "./GameExitConfirmDialog";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Switch } from "@/components/ui/switch";
import { Card as CardType } from "@/utils/game/cardUtils";
import { getCardImage } from "@/utils/game/cardImages";
import { isDebugModeEnabled } from "@/utils/debug/debugMode";

type GameMenuProps = {
  showHelpSheet: boolean;
  setShowHelpSheet: (value: boolean) => void;
  handleOpenMenu: () => void;
  handleReturnToMenu: () => void;
  gameState: {
    trumpSuit?: string;
    message: string;
    gameScore: number[];
    teams: {
      tricksWon: CardType[];
    }[];
    lastTrickWinner: number | null;
    victoryPoints?: number;
    players: {
      name: string;
    }[];
    currentTrick?: CardType[];
    trickNumber?: number;
  };
  difficulty: string;
  getTrumpSuitImage: () => string;
  getTrumpSuitName: () => string;
  isMobile: boolean;
  showDebugModal?: boolean;
  setShowDebugModal?: (show: boolean) => void;
};

const GameMenu: React.FC<GameMenuProps> = ({
  showHelpSheet,
  setShowHelpSheet,
  handleOpenMenu,
  handleReturnToMenu,
  gameState,
  difficulty,
  getTrumpSuitImage,
  getTrumpSuitName,
  isMobile,
  showDebugModal = false,
  setShowDebugModal,
}) => {
  const [showExitConfirm, setShowExitConfirm] = useState(false);
  const {
    playSound,
    playMusic,
    stopMusic,
    setEffectsEnabled,
    setMusicEnabled,
    getConfig,
    isTrackPlaying,
  } = useAudio();

  // Initialize audio settings from AudioManager
  const audioConfig = getConfig();
  const [soundEnabled, setSoundEnabled] = useState(
    audioConfig.soundEffectsEnabled
  );
  const [musicEnabled, setMusicEnabledLocal] = useState(
    audioConfig.musicEnabled
  );

  // Sync local state with AudioManager when values actually change
  useEffect(() => {
    const config = getConfig();
    if (config.soundEffectsEnabled !== soundEnabled) {
      setSoundEnabled(config.soundEffectsEnabled);
    }
    if (config.musicEnabled !== musicEnabled) {
      setMusicEnabledLocal(config.musicEnabled);
    }
  }, [getConfig, soundEnabled, musicEnabled]);

  const handleToggleSoundEffects = (enabled: boolean) => {
    playSound("buttonClick");
    setSoundEnabled(enabled);
    setEffectsEnabled(enabled);
  };

  const handleToggleMusic = (enabled: boolean) => {
    playSound("buttonClick");
    setMusicEnabledLocal(enabled);
    setMusicEnabled(enabled);
    if (!enabled) {
      stopMusic();
    } else {
      // Only start music if not already playing the game track
      if (!isTrackPlaying("game")) {
        playMusic("game");
      }
    }
  };

  const handleExitClick = () => {
    playSound("buttonClick");
    setShowExitConfirm(true);
  };

  // Funzione per ottenere le carte dell'ultima presa completata
  const getLastTrick = (): CardType[] => {
    // Prendi tutte le carte vinte da entrambi i team
    const allTricksWon = [
      ...gameState.teams[0].tricksWon,
      ...gameState.teams[1].tricksWon,
    ];

    // Se non ci sono carte vinte, non c'è nessuna presa da mostrare
    if (allTricksWon.length === 0) {
      return [];
    }

    // Calcola quante prese complete ci sono
    const completedTricks = Math.floor(allTricksWon.length / 4);

    if (completedTricks === 0) {
      return [];
    }

    // Determina quale presa mostrare basandosi sul numero del trick corrente
    const currentTrickNumber = gameState.trickNumber || 1;
    const currentTrickInProgress =
      gameState.currentTrick && gameState.currentTrick.length > 0;

    // Se siamo nel primo trick, non c'è nessuna presa precedente da mostrare
    if (currentTrickNumber === 1) {
      return [];
    }

    // Mostra sempre la presa del turno precedente
    // Se siamo nel trick N, mostra la presa del trick N-1
    const trickToShow = Math.max(1, currentTrickNumber - 1);

    // Assicurati che la presa da mostrare sia effettivamente completata
    if (trickToShow > completedTricks) {
      return [];
    }

    // Prendi le 4 carte della presa da mostrare (1-indexed, quindi sottrai 1)
    const startIndex = (trickToShow - 1) * 4;
    const endIndex = startIndex + 4;
    const lastTrickCards = allTricksWon.slice(startIndex, endIndex);

    console.log(
      `🎯 Ultima presa: trick ${trickToShow} (corrente: ${currentTrickNumber}), carte ${startIndex}-${
        endIndex - 1
      }, completedTricks: ${completedTricks}, currentTrick in corso: ${currentTrickInProgress}`
    );

    return lastTrickCards.length === 4 ? lastTrickCards : [];
  };

  const hasLastTrick = () => {
    return getLastTrick().length === 4;
  };

  const handleConfirmExit = () => {
    setShowExitConfirm(false);
    handleReturnToMenu();
  };

  const renderDifficultyBadge = () => {
    const badgeClasses = {
      easy: "bg-green-100 text-green-800 border-green-300",
      medium: "bg-yellow-100 text-yellow-800 border-yellow-300",
      hard: "bg-red-100 text-red-800 border-red-300",
    };

    const difficultyText =
      difficulty === "easy"
        ? "Principiante"
        : difficulty === "medium"
        ? "Esperto"
        : "Maestro";

    return (
      <span
        className={`px-2 py-1 rounded-md border ${
          badgeClasses[difficulty as keyof typeof badgeClasses]
        }`}
      >
        {difficultyText}
      </span>
    );
  };

  const handleSheetOpenChange = (open: boolean) => {
    if (!open) {
      playSound("buttonClick"); // Play sound when menu closes
    }
    setShowHelpSheet(open);
  };

  return (
    <>
      <Sheet open={showHelpSheet} onOpenChange={handleSheetOpenChange}>
        <SheetTrigger asChild>
          <ActionButton
            variant="outline"
            onClick={handleOpenMenu}
            className={cn(
              "flex items-center justify-center bg-yellow-50/90 border border-red-200 p-2 rounded-lg shadow-sm text-red-800 font-medium relative z-10",
              isMobile ? "h-12 w-12 text-xs" : "h-12 w-20 text-m"
            )}
            size={isMobile ? "sm" : "md"}
          >
            <Menu
              className={cn("text-red-600", isMobile ? "h-6 w-6" : "h-7 w-7")}
            />
            {!isMobile && <span className="text-red-600 ml-1">Menù</span>}
          </ActionButton>
        </SheetTrigger>{" "}
        <SheetContent
          side="left"
          className="bg-gradient-to-br from-amber-100 to-orange-200 border-r border-amber-300 flex flex-col h-full gap-0 p-2 z-[50]"
          style={{ paddingTop: `calc(0.5rem + env(safe-area-inset-top, 0px))` }}
        >
          <SheetHeader className="flex-shrink-0 pb-2 border-b border-amber-400">
            <div className="flex items-center justify-between">
              <SheetTitle
                className="text-transparent bg-gradient-to-r from-amber-800 via-red-700 to-amber-900 bg-clip-text text-2xl font-bold flex items-center gap-3 drop-shadow-sm"
                style={{ fontFamily: "'DynaPuff', cursive" }}
              >
                Menu di Gioco
              </SheetTitle>
              <button
                onClick={() => setShowHelpSheet(false)}
                className="p-2 hover:bg-amber-200/50 rounded-lg transition-colors"
              >
                <X className="h-7 w-7 text-amber-800" />
              </button>
            </div>
          </SheetHeader>
          <div className="pt-4 space-y-4 flex-1 overflow-y-auto p-1 scrollbar-thin scrollbar-thumb-amber-400 scrollbar-track-transparent">
            <button
              onClick={handleExitClick}
              className="w-full flex items-center gap-2 px-3 py-2 rounded-lg border border-red-600 bg-red-600 text-white font-medium hover:bg-red-700 hover:border-red-700 transition duration-200 shadow-sm"
            >
              <LogOut className="h-4 w-4 text-white" />
              Abbandona partita
            </button>{" "}
            {/* Questa Partita */}
            <div className="p-3 rounded-lg bg-amber-50 border border-amber-300 shadow-sm">
              <h3
                className="font-medium text-amber-900 flex items-center gap-2 mb-3"
                style={{ fontFamily: "'DynaPuff', cursive" }}
              >
                <img
                  src="/images/stremline-icons/gamedice.png"
                  alt="Game Info"
                  className="h-6 w-6"
                />
                Questa Partita
              </h3>

              {/* Ultima Presa */}
              {hasLastTrick() && (
                <div className="mb-4">
                  {" "}
                  <h4
                    className="font-medium text-amber-900 flex items-center gap-2 mb-2 text-sm"
                    style={{ fontFamily: "'DynaPuff', cursive" }}
                  >
                    Ultima Presa
                  </h4>{" "}
                  <div className="grid grid-cols-4 gap-1 mb-2">
                    {getLastTrick().map((card, index) => (
                      <div
                        key={`${card.suit}-${card.rank}-${index}`}
                        className="aspect-[3/4] bg-white rounded border shadow-sm overflow-hidden cursor-pointer transform transition-transform duration-200 hover:scale-110 hover:z-10 relative"
                        style={{ height: "60px" }}
                        title={`${card.rank} di ${card.suit}`}
                      >
                        <img
                          src={getCardImage(card.suit, card.rank)}
                          alt={`${card.rank} di ${card.suit}`}
                          className="w-full h-full object-contain"
                        />
                      </div>
                    ))}
                  </div>
                  {gameState.lastTrickWinner !== null && (
                    <p className="text-xs text-amber-700 mb-3 text-center">
                      Vinta da:{" "}
                      {gameState.players[gameState.lastTrickWinner].name}
                    </p>
                  )}
                </div>
              )}

              {/* Briscola e Difficoltà */}
              <div className="space-y-3">
                {" "}
                <div>
                  <h4
                    className="font-medium text-amber-900 flex items-center gap-2 mb-1 text-sm"
                    style={{ fontFamily: "'DynaPuff', cursive" }}
                  >
                    Briscola
                  </h4>
                  <p className="text-amber-800 font-medium text-sm">
                    {gameState.trumpSuit ? (
                      <span className="flex items-center gap-2">
                        <img
                          src={getTrumpSuitImage()}
                          alt={getTrumpSuitName()}
                          className="w-4 h-4 object-contain transform -rotate-12"
                        />
                        {getTrumpSuitName()}
                      </span>
                    ) : (
                      "Non ancora selezionata"
                    )}
                  </p>
                </div>
                <div>
                  <h4
                    className="font-medium text-amber-900 flex items-center gap-2 mb-1 text-sm"
                    style={{ fontFamily: "'DynaPuff', cursive" }}
                  >
                    Modalità
                  </h4>
                  <div className="flex items-center gap-4">
                    <div className="text-amber-800 font-medium flex items-center gap-2 text-sm">
                      {renderDifficultyBadge()}
                    </div>
                    <p className="text-amber-800 font-medium text-sm">
                      {gameState.victoryPoints || 31} punti
                    </p>
                  </div>
                </div>
              </div>
            </div>{" "}
            {/* Controlli Audio */}
            <div className="p-4 rounded-lg bg-amber-50 border border-amber-300 shadow-sm">
              <h3
                className="font-medium text-amber-900 flex items-center gap-2 mb-3"
                style={{ fontFamily: "'DynaPuff', cursive" }}
              >
                <img
                  src="/images/stremline-icons/speakervolume.png"
                  alt="Audio"
                  className="h-6 w-6"
                />
                Audio
              </h3>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-amber-800">
                      Effetti Sonori
                    </span>
                  </div>
                  <Switch
                    checked={soundEnabled}
                    onCheckedChange={handleToggleSoundEffects}
                    className="data-[state=checked]:bg-amber-700"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-amber-800">
                      Musica di Sottofondo
                    </span>
                  </div>
                  <Switch
                    checked={musicEnabled}
                    onCheckedChange={handleToggleMusic}
                    className="data-[state=checked]:bg-amber-700"
                  />
                </div>{" "}
              </div>
            </div>{" "}
            {/* Sezione Debug Mode - visibile solo se debug è attivo */}
            {isDebugModeEnabled() && (
              <div className="p-4 rounded-lg bg-red-50 border border-red-300 shadow-sm">
                <h3
                  className="font-medium text-red-900 flex items-center gap-2 mb-3"
                  style={{ fontFamily: "'DynaPuff', cursive" }}
                >
                  <Eye className="h-5 w-5" />
                  Debug Mode
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-red-800">
                        Mostra Carte AI
                      </span>
                    </div>
                    <Switch
                      checked={showDebugModal}
                      onCheckedChange={(checked) =>
                        setShowDebugModal && setShowDebugModal(checked)
                      }
                      className="data-[state=checked]:bg-red-700"
                    />
                  </div>
                  <div className="text-xs text-red-600 bg-red-100 p-2 rounded">
                    🔍 Debug attivo: puoi vedere le carte degli altri giocatori
                    per testare la logica CPU
                  </div>
                </div>
              </div>
            )}
            <Accordion type="single" collapsible className="w-full">
              <div className="p-4 rounded-lg bg-amber-50 border border-amber-300 shadow-sm">
                <h3
                  className="font-medium text-amber-900 flex items-center gap-2 mb-3"
                  style={{ fontFamily: "'DynaPuff', cursive" }}
                >
                  <img
                    src="/images/stremline-icons/openbook.png"
                    alt="Rules"
                    className="h-6 w-6"
                  />
                  Regole
                </h3>
                <AccordionItem
                  value="card-values"
                  className="border-b border-amber-200 last:border-b-0"
                >
                  {" "}
                  <AccordionTrigger className="p-3 hover:bg-amber-100/50 transition-colors rounded-lg">
                    <h3
                      className="font-medium text-amber-900 flex items-center gap-2 text-base"
                      style={{ fontFamily: "'DynaPuff', cursive" }}
                    >
                      Valori delle carte
                    </h3>
                  </AccordionTrigger>
                  <AccordionContent className="p-3 pt-0">
                    <div className="text-sm text-amber-800 space-y-2">
                      <div className="flex justify-between items-center border-b border-amber-200 pb-1">
                        <span className="font-medium">Carta</span>
                        <span className="font-medium">Valore</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Asso</span>
                        <span className="font-semibold">1 punto (3/3)</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Tre</span>
                        <span>1/3 punto</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Re</span>
                        <span>1/3 punto</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Cavallo</span>
                        <span>1/3 punto</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Fante</span>
                        <span>1/3 punto</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Due</span>
                        <span>1/3 punto</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>7, 6, 5, 4</span>
                        <span>0 punti</span>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem
                  value="communication"
                  className="border-b border-amber-200 last:border-b-0"
                >
                  {" "}
                  <AccordionTrigger className="p-3 hover:bg-amber-100/50 transition-colors rounded-lg">
                    <h3
                      className="font-medium text-amber-900 flex items-center gap-2 text-base"
                      style={{ fontFamily: "'DynaPuff', cursive" }}
                    >
                      Parole
                    </h3>
                  </AccordionTrigger>
                  <AccordionContent className="p-3 pt-0">
                    <div className="text-sm text-amber-800 space-y-3">
                      <p className="font-medium text-amber-900 mb-3">
                        Solo chi apre il turno può comunicare al compagno:
                      </p>

                      <div className="space-y-3">
                        <div className="p-2 bg-red-50/60 rounded-lg border border-red-200">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-lg">👊</span>
                            <span className="font-semibold text-red-700">
                              Busso
                            </span>
                          </div>
                          <p className="text-xs text-red-600">
                            "Prendi questa mano e la prossima gioca ancora
                            questo seme che prendo io!"
                          </p>
                        </div>

                        <div className="p-2 bg-yellow-50/60 rounded-lg border border-yellow-200">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-lg">🐍</span>
                            <span className="font-semibold text-yellow-700">
                              Striscio
                            </span>
                          </div>
                          <p className="text-xs text-yellow-600">
                            "Ne ho ancora una di questo seme"
                          </p>
                        </div>

                        <div className="p-2 bg-blue-50/60 rounded-lg border border-blue-200">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-lg">🕊️</span>
                            <span className="font-semibold text-blue-700">
                              Volo
                            </span>
                          </div>
                          <p className="text-xs text-blue-600">
                            "Non ho più carte di questo seme, ora posso tagliare
                            con briscola"
                          </p>
                        </div>
                      </div>

                      <div className="mt-3 p-2 bg-amber-50/60 rounded-lg border border-amber-200">
                        <p className="text-xs text-amber-700 italic">
                          💡 Queste comunicazioni sono parte della tradizione
                          del gioco e aiutano la strategia di squadra.
                        </p>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>{" "}
                <AccordionItem value="game-rules" className="border-b-0">
                  <AccordionTrigger className="p-3 hover:bg-amber-100/50 transition-colors rounded-lg">
                    <h3
                      className="font-medium text-amber-900 flex items-center gap-2 text-base"
                      style={{ fontFamily: "'DynaPuff', cursive" }}
                    >
                      Regole di base
                    </h3>
                  </AccordionTrigger>
                  <AccordionContent className="p-3 pt-0">
                    <ul className="text-sm text-amber-800 space-y-2 list-disc pl-5">
                      <li>
                        Il Marafone è un gioco a squadre (2 vs 2) con 10 carte
                        per giocatore.
                      </li>
                      <li>
                        Il seme di briscola viene scelto dal giocatore che ha il
                        4 di denari (o dal giocatore successivo nei round
                        seguenti).
                      </li>
                      <li>
                        È obbligatorio rispondere a colore se si possiede una
                        carta del seme giocato, anche se il compagno sta
                        vincendo.
                      </li>
                      <li>
                        Se non si hanno carte del seme giocato è obbligatorio
                        giocare briscola, a meno che non sia già stata giocata
                        una briscola più alta.
                      </li>
                      <li>
                        La combinazione di Asso, 2 e 3 dello stesso seme è detta
                        "Maraffa" e vale 3 punti bonus per chi sceglie quel seme
                        come briscola.
                      </li>
                      <li>
                        Chi vince l'ultima presa (10ª) ottiene 1 punto bonus.
                      </li>
                      <li>Il gioco si vince raggiungendo 31 punti totali.</li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              </div>
            </Accordion>
          </div>
        </SheetContent>
      </Sheet>

      {/* Dialogo di conferma uscita */}
      <GameExitConfirmDialog
        isOpen={showExitConfirm}
        onClose={() => setShowExitConfirm(false)}
        onConfirm={handleConfirmExit}
      />
    </>
  );
};

export default GameMenu;
