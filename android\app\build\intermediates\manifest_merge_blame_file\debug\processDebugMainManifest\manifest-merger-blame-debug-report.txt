1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.eliazavatta.maraffa"
4    android:versionCode="27"
5    android:versionName="2.9" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:71:5-67
13-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:71:22-64
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:72:5-79
14-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:72:22-76
15    <uses-permission android:name="android.permission.VIBRATE" />
15-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:73:5-66
15-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:73:22-63
16    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
16-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:74:5-79
16-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:74:22-76
17    <uses-permission android:name="com.android.vending.BILLING" />
17-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:75:5-67
17-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:75:22-64
18
19    <!-- Supporto per schermi grandi e diverse densità -->
20    <supports-screens
20-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:78:5-84:37
21        android:anyDensity="true"
21-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:83:9-34
22        android:largeScreens="true"
22-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:81:9-36
23        android:normalScreens="true"
23-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:80:9-37
24        android:resizeable="true"
24-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:84:9-34
25        android:smallScreens="true"
25-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:79:9-36
26        android:xlargeScreens="true" />
26-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:82:9-37
27
28    <!-- Supporto per diverse configurazioni hardware -->
29    <uses-feature
29-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:87:5-89:36
30        android:name="android.hardware.screen.portrait"
30-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:88:9-56
31        android:required="false" />
31-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:89:9-33
32    <uses-feature
32-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:90:5-92:36
33        android:name="android.hardware.screen.landscape"
33-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:91:9-57
34        android:required="false" />
34-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:92:9-33
35
36    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
36-->[:codetrix-studio-capacitor-google-auth] C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\node_modules\@codetrix-studio\capacitor-google-auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-74
36-->[:codetrix-studio-capacitor-google-auth] C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\node_modules\@codetrix-studio\capacitor-google-auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-71
37
38    <queries>
38-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:12:5-19:15
39        <intent>
39-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:13:9-15:18
40            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
40-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:14:13-91
40-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:14:21-88
41        </intent>
42        <intent>
42-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:16:9-18:18
43            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
43-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:17:13-116
43-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:17:21-113
44        </intent>
45        <!-- For browser content -->
46        <intent>
46-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:38:9-44:18
47            <action android:name="android.intent.action.VIEW" />
47-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:29:17-69
47-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:29:25-66
48
49            <category android:name="android.intent.category.BROWSABLE" />
49-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:31:17-78
49-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:31:27-75
50
51            <data android:scheme="https" />
51-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:32:17-66
51-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:32:23-63
52        </intent> <!-- End of browser content -->
53        <!-- For CustomTabsService -->
54        <intent>
54-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:47:9-49:18
55            <action android:name="android.support.customtabs.action.CustomTabsService" />
55-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:48:13-90
55-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:48:21-87
56        </intent>
57    </queries>
58
59    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
59-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:27:5-82
59-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:27:22-79
60    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
60-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:28:5-88
60-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:28:22-85
61    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
61-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:29:5-83
61-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:29:22-80
62    <uses-permission android:name="android.permission.WAKE_LOCK" />
62-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:25:5-68
62-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:25:22-65
63    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
63-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:26:5-110
63-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:26:22-107
64    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
64-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
64-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
65
66    <permission
66-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
67        android:name="com.eliazavatta.maraffa.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
67-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
68        android:protectionLevel="signature" />
68-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
69
70    <uses-permission android:name="com.eliazavatta.maraffa.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
70-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
70-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
71
72    <application
72-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:4:5-67:19
73        android:allowAudioPlaybackCapture="false"
73-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:11:9-50
74        android:allowBackup="true"
74-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:5:9-35
75        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
75-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
76        android:debuggable="true"
77        android:extractNativeLibs="false"
78        android:icon="@mipmap/ic_launcher"
78-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:6:9-43
79        android:label="@string/app_name"
79-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:7:9-41
80        android:roundIcon="@mipmap/ic_launcher_round"
80-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:8:9-54
81        android:supportsRtl="true"
81-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:9:9-35
82        android:theme="@style/AppTheme" >
82-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:10:9-40
83        <activity
83-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:13:9-35:20
84            android:name="com.eliazavatta.maraffa.MainActivity"
84-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:15:13-41
85            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
85-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:14:13-140
86            android:exported="true"
86-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:19:13-36
87            android:label="@string/title_activity_main"
87-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:16:13-56
88            android:launchMode="singleTask"
88-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:18:13-44
89            android:resizeableActivity="true"
89-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:20:13-46
90            android:supportsPictureInPicture="false"
90-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:21:13-53
91            android:theme="@style/AppTheme.NoActionBarLaunch" >
91-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:17:13-62
92            <intent-filter>
92-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:22:13-25:29
93                <action android:name="android.intent.action.MAIN" />
93-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:23:17-69
93-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:23:25-66
94
95                <category android:name="android.intent.category.LAUNCHER" />
95-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:24:17-77
95-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:24:27-74
96            </intent-filter>
97
98            <!-- Deep link per OAuth redirect -->
99            <intent-filter android:autoVerify="true" >
99-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:28:13-33:29
99-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:28:28-53
100                <action android:name="android.intent.action.VIEW" />
100-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:29:17-69
100-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:29:25-66
101
102                <category android:name="android.intent.category.DEFAULT" />
102-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:30:17-76
102-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:30:27-73
103                <category android:name="android.intent.category.BROWSABLE" />
103-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:31:17-78
103-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:31:27-75
104
105                <data android:scheme="com.eliazavatta.maraffa" />
105-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:32:17-66
105-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:32:23-63
106            </intent-filter>
107        </activity>
108
109        <provider
110            android:name="androidx.core.content.FileProvider"
110-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:38:13-62
111            android:authorities="com.eliazavatta.maraffa.fileprovider"
111-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:39:13-64
112            android:exported="false"
112-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:40:13-37
113            android:grantUriPermissions="true" >
113-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:41:13-47
114            <meta-data
114-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:42:13-44:64
115                android:name="android.support.FILE_PROVIDER_PATHS"
115-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:43:17-67
116                android:resource="@xml/file_paths" />
116-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:44:17-51
117        </provider>
118
119        <!-- AdMob App ID -->
120        <meta-data
120-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:48:9-50:70
121            android:name="com.google.android.gms.ads.APPLICATION_ID"
121-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:49:13-69
122            android:value="ca-app-pub-3013811216506035~**********" />
122-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:50:13-67
123
124        <!-- Dichiarazione esplicita per l'uso dell'AD_ID -->
125        <meta-data
125-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:53:9-55:36
126            android:name="com.google.android.gms.ads.AD_ID_USAGE"
126-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:54:13-66
127            android:value="true" />
127-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:55:13-33
128
129        <!-- Attribution tag per Android 11+ -->
130        <meta-data
130-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:58:9-60:55
131            android:name="android.app.attribution"
131-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:59:13-51
132            android:value="com.eliazavatta.maraffa" />
132-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:60:13-52
133
134        <!-- Google Play Services version -->
135        <meta-data
135-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:63:9-65:69
136            android:name="com.google.android.gms.version"
136-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:64:13-58
137            android:value="@integer/google_play_services_version" />
137-->C:\Users\<USER>\Desktop\Progetti\maraffa-romagnola\android\app\src\main\AndroidManifest.xml:65:13-66
138
139        <activity
139-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:23:9-27:75
140            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
140-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:24:13-93
141            android:excludeFromRecents="true"
141-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:25:13-46
142            android:exported="false"
142-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:26:13-37
143            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
143-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:27:13-72
144        <!--
145            Service handling Google Sign-In user revocation. For apps that do not integrate with
146            Google Sign-In, this service will never be started.
147        -->
148        <service
148-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:33:9-37:51
149            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
149-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:34:13-89
150            android:exported="true"
150-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:35:13-36
151            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
151-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:36:13-107
152            android:visibleToInstantApps="true" />
152-->[com.google.android.gms:play-services-auth:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9f356af7ade0d386b38fe5cf6881d\transformed\play-services-auth-18.1.0\AndroidManifest.xml:37:13-48
153
154        <receiver
154-->[com.revenuecat.purchases:purchases-store-amazon:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4f1bb42d5a19a9f997259548e65a28\transformed\purchases-store-amazon-8.22.0\AndroidManifest.xml:8:9-15:20
155            android:name="com.amazon.device.iap.ResponseReceiver"
155-->[com.revenuecat.purchases:purchases-store-amazon:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4f1bb42d5a19a9f997259548e65a28\transformed\purchases-store-amazon-8.22.0\AndroidManifest.xml:9:13-66
156            android:exported="true"
156-->[com.revenuecat.purchases:purchases-store-amazon:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4f1bb42d5a19a9f997259548e65a28\transformed\purchases-store-amazon-8.22.0\AndroidManifest.xml:10:13-36
157            android:permission="com.amazon.inapp.purchasing.Permission.NOTIFY" >
157-->[com.revenuecat.purchases:purchases-store-amazon:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4f1bb42d5a19a9f997259548e65a28\transformed\purchases-store-amazon-8.22.0\AndroidManifest.xml:11:13-79
158            <intent-filter>
158-->[com.revenuecat.purchases:purchases-store-amazon:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4f1bb42d5a19a9f997259548e65a28\transformed\purchases-store-amazon-8.22.0\AndroidManifest.xml:12:13-14:29
159                <action android:name="com.amazon.inapp.purchasing.NOTIFY" />
159-->[com.revenuecat.purchases:purchases-store-amazon:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4f1bb42d5a19a9f997259548e65a28\transformed\purchases-store-amazon-8.22.0\AndroidManifest.xml:13:17-77
159-->[com.revenuecat.purchases:purchases-store-amazon:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4f1bb42d5a19a9f997259548e65a28\transformed\purchases-store-amazon-8.22.0\AndroidManifest.xml:13:25-74
160            </intent-filter>
161        </receiver>
162
163        <activity
163-->[com.revenuecat.purchases:purchases:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e71e55f1b3f1b9ece32afceeeec4d6ac\transformed\purchases-8.22.0\AndroidManifest.xml:10:9-13:75
164            android:name="com.revenuecat.purchases.amazon.purchasing.ProxyAmazonBillingActivity"
164-->[com.revenuecat.purchases:purchases:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e71e55f1b3f1b9ece32afceeeec4d6ac\transformed\purchases-8.22.0\AndroidManifest.xml:11:13-97
165            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
165-->[com.revenuecat.purchases:purchases:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e71e55f1b3f1b9ece32afceeeec4d6ac\transformed\purchases-8.22.0\AndroidManifest.xml:12:13-96
166            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
166-->[com.revenuecat.purchases:purchases:8.22.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e71e55f1b3f1b9ece32afceeeec4d6ac\transformed\purchases-8.22.0\AndroidManifest.xml:13:13-72
167
168        <meta-data
168-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:22:9-24:37
169            android:name="com.google.android.play.billingclient.version"
169-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:23:13-73
170            android:value="7.1.1" />
170-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:24:13-34
171
172        <activity
172-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:26:9-30:75
173            android:name="com.android.billingclient.api.ProxyBillingActivity"
173-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:27:13-78
174            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
174-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:28:13-96
175            android:exported="false"
175-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:29:13-37
176            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
176-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:30:13-72
177        <activity
177-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:31:9-35:75
178            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
178-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:32:13-80
179            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
179-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:33:13-96
180            android:exported="false"
180-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:34:13-37
181            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
181-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9845262327b0dd50c4969eecc4c29272\transformed\billing-7.1.1\AndroidManifest.xml:35:13-72
182        <activity
182-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
183            android:name="com.google.android.gms.common.api.GoogleApiActivity"
183-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
184            android:exported="false"
184-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
185            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
185-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
186        <activity
186-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:56:9-61:43
187            android:name="com.google.android.gms.ads.AdActivity"
187-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:57:13-65
188            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
188-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:58:13-122
189            android:exported="false"
189-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:59:13-37
190            android:theme="@android:style/Theme.Translucent" />
190-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:60:13-61
191
192        <provider
192-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:63:9-68:43
193            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
193-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:64:13-76
194            android:authorities="com.eliazavatta.maraffa.mobileadsinitprovider"
194-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:65:13-73
195            android:exported="false"
195-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:66:13-37
196            android:initOrder="100" />
196-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:67:13-36
197
198        <service
198-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:70:9-74:43
199            android:name="com.google.android.gms.ads.AdService"
199-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:71:13-64
200            android:enabled="true"
200-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:72:13-35
201            android:exported="false" />
201-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:73:13-37
202
203        <activity
203-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:76:9-80:43
204            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
204-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:77:13-82
205            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
205-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:78:13-122
206            android:exported="false" />
206-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:79:13-37
207        <activity
207-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:81:9-88:43
208            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
208-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:82:13-82
209            android:excludeFromRecents="true"
209-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:83:13-46
210            android:exported="false"
210-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:84:13-37
211            android:launchMode="singleTask"
211-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:85:13-44
212            android:taskAffinity=""
212-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:86:13-36
213            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
213-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:87:13-72
214
215        <property
215-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:90:9-92:62
216            android:name="android.adservices.AD_SERVICES_CONFIG"
216-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:91:13-65
217            android:resource="@xml/gma_ad_services_config" />
217-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe26fb25229d506327567836af33af01\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:92:13-59
218
219        <receiver
219-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:29:9-33:20
220            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
220-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:30:13-85
221            android:enabled="true"
221-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:31:13-35
222            android:exported="false" >
222-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:32:13-37
223        </receiver>
224
225        <service
225-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:35:9-38:40
226            android:name="com.google.android.gms.measurement.AppMeasurementService"
226-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:36:13-84
227            android:enabled="true"
227-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:37:13-35
228            android:exported="false" />
228-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:38:13-37
229        <service
229-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:39:9-43:72
230            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
230-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:40:13-87
231            android:enabled="true"
231-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:41:13-35
232            android:exported="false"
232-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:42:13-37
233            android:permission="android.permission.BIND_JOB_SERVICE" />
233-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\503dac5d8d473c16443f07aa6a78d11e\transformed\play-services-measurement-21.2.2\AndroidManifest.xml:43:13-69
234        <service
234-->[com.google.android.gms:play-services-measurement-api:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae27ebad8b432bd935cbb5cc436e8c1c\transformed\play-services-measurement-api-21.2.2\AndroidManifest.xml:28:9-34:19
235            android:name="com.google.firebase.components.ComponentDiscoveryService"
235-->[com.google.android.gms:play-services-measurement-api:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae27ebad8b432bd935cbb5cc436e8c1c\transformed\play-services-measurement-api-21.2.2\AndroidManifest.xml:29:13-84
236            android:directBootAware="true"
236-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b187c9076cc817781505e7d9e26441a\transformed\firebase-common-20.2.0\AndroidManifest.xml:34:13-43
237            android:exported="false" >
237-->[com.google.android.gms:play-services-measurement-api:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae27ebad8b432bd935cbb5cc436e8c1c\transformed\play-services-measurement-api-21.2.2\AndroidManifest.xml:30:13-37
238            <meta-data
238-->[com.google.android.gms:play-services-measurement-api:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae27ebad8b432bd935cbb5cc436e8c1c\transformed\play-services-measurement-api-21.2.2\AndroidManifest.xml:31:13-33:85
239                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
239-->[com.google.android.gms:play-services-measurement-api:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae27ebad8b432bd935cbb5cc436e8c1c\transformed\play-services-measurement-api-21.2.2\AndroidManifest.xml:32:17-139
240                android:value="com.google.firebase.components.ComponentRegistrar" />
240-->[com.google.android.gms:play-services-measurement-api:21.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae27ebad8b432bd935cbb5cc436e8c1c\transformed\play-services-measurement-api-21.2.2\AndroidManifest.xml:33:17-82
241            <meta-data
241-->[com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19e36564445c6d2c9b6ee397781d806e\transformed\firebase-installations-17.0.1\AndroidManifest.xml:18:13-20:85
242                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
242-->[com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19e36564445c6d2c9b6ee397781d806e\transformed\firebase-installations-17.0.1\AndroidManifest.xml:19:17-127
243                android:value="com.google.firebase.components.ComponentRegistrar" />
243-->[com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19e36564445c6d2c9b6ee397781d806e\transformed\firebase-installations-17.0.1\AndroidManifest.xml:20:17-82
244        </service>
245
246        <provider
246-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b187c9076cc817781505e7d9e26441a\transformed\firebase-common-20.2.0\AndroidManifest.xml:25:9-30:39
247            android:name="com.google.firebase.provider.FirebaseInitProvider"
247-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b187c9076cc817781505e7d9e26441a\transformed\firebase-common-20.2.0\AndroidManifest.xml:26:13-77
248            android:authorities="com.eliazavatta.maraffa.firebaseinitprovider"
248-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b187c9076cc817781505e7d9e26441a\transformed\firebase-common-20.2.0\AndroidManifest.xml:27:13-72
249            android:directBootAware="true"
249-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b187c9076cc817781505e7d9e26441a\transformed\firebase-common-20.2.0\AndroidManifest.xml:28:13-43
250            android:exported="false"
250-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b187c9076cc817781505e7d9e26441a\transformed\firebase-common-20.2.0\AndroidManifest.xml:29:13-37
251            android:initOrder="100" />
251-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b187c9076cc817781505e7d9e26441a\transformed\firebase-common-20.2.0\AndroidManifest.xml:30:13-36
252        <provider
252-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
253            android:name="androidx.startup.InitializationProvider"
253-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
254            android:authorities="com.eliazavatta.maraffa.androidx-startup"
254-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
255            android:exported="false" >
255-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
256            <meta-data
256-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
257                android:name="androidx.emoji2.text.EmojiCompatInitializer"
257-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
258                android:value="androidx.startup" />
258-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
259            <meta-data
259-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
260                android:name="androidx.work.WorkManagerInitializer"
260-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
261                android:value="androidx.startup" />
261-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
262            <meta-data
262-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
263                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
263-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
264                android:value="androidx.startup" />
264-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
265            <meta-data
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
266                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
267                android:value="androidx.startup" />
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
268        </provider>
269
270        <uses-library
270-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b887b00b49cf9b23589e338bd66b82c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
271            android:name="android.ext.adservices"
271-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b887b00b49cf9b23589e338bd66b82c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
272            android:required="false" />
272-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b887b00b49cf9b23589e338bd66b82c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
273
274        <service
274-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
275            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
275-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
276            android:directBootAware="false"
276-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
277            android:enabled="@bool/enable_system_alarm_service_default"
277-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
278            android:exported="false" />
278-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
279        <service
279-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
280            android:name="androidx.work.impl.background.systemjob.SystemJobService"
280-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
281            android:directBootAware="false"
281-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
282            android:enabled="@bool/enable_system_job_service_default"
282-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
283            android:exported="true"
283-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
284            android:permission="android.permission.BIND_JOB_SERVICE" />
284-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
285        <service
285-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
286            android:name="androidx.work.impl.foreground.SystemForegroundService"
286-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
287            android:directBootAware="false"
287-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
288            android:enabled="@bool/enable_system_foreground_service_default"
288-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
289            android:exported="false" />
289-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
290
291        <receiver
291-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
292            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
292-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
293            android:directBootAware="false"
293-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
294            android:enabled="true"
294-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
295            android:exported="false" />
295-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
296        <receiver
296-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
297            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
297-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
298            android:directBootAware="false"
298-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
299            android:enabled="false"
299-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
300            android:exported="false" >
300-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
301            <intent-filter>
301-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
302                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
302-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
302-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
303                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
303-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
303-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
304            </intent-filter>
305        </receiver>
306        <receiver
306-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
307            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
307-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
308            android:directBootAware="false"
308-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
309            android:enabled="false"
309-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
310            android:exported="false" >
310-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
311            <intent-filter>
311-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
312                <action android:name="android.intent.action.BATTERY_OKAY" />
312-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
312-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
313                <action android:name="android.intent.action.BATTERY_LOW" />
313-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
313-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
314            </intent-filter>
315        </receiver>
316        <receiver
316-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
317            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
317-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
318            android:directBootAware="false"
318-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
319            android:enabled="false"
319-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
320            android:exported="false" >
320-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
321            <intent-filter>
321-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
322                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
322-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
322-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
323                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
323-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
323-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
324            </intent-filter>
325        </receiver>
326        <receiver
326-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
327            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
327-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
328            android:directBootAware="false"
328-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
329            android:enabled="false"
329-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
330            android:exported="false" >
330-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
331            <intent-filter>
331-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
332                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
332-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
332-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
333            </intent-filter>
334        </receiver>
335        <receiver
335-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
336            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
336-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
337            android:directBootAware="false"
337-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
338            android:enabled="false"
338-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
339            android:exported="false" >
339-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
340            <intent-filter>
340-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
341                <action android:name="android.intent.action.BOOT_COMPLETED" />
341-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
341-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
342                <action android:name="android.intent.action.TIME_SET" />
342-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
342-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
343                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
343-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
343-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
344            </intent-filter>
345        </receiver>
346        <receiver
346-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
347            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
347-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
348            android:directBootAware="false"
348-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
349            android:enabled="@bool/enable_system_alarm_service_default"
349-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
350            android:exported="false" >
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
351            <intent-filter>
351-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
352                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
352-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
352-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
353            </intent-filter>
354        </receiver>
355        <receiver
355-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
356            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
356-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
357            android:directBootAware="false"
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
358            android:enabled="true"
358-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
359            android:exported="true"
359-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
360            android:permission="android.permission.DUMP" >
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
361            <intent-filter>
361-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
362                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdb33c510cdc9b63ed1a3a185bb4252\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
363            </intent-filter>
364        </receiver>
365        <receiver
365-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
366            android:name="androidx.profileinstaller.ProfileInstallReceiver"
366-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
367            android:directBootAware="false"
367-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
368            android:enabled="true"
368-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
369            android:exported="true"
369-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
370            android:permission="android.permission.DUMP" >
370-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
371            <intent-filter>
371-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
372                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
372-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
372-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
373            </intent-filter>
374            <intent-filter>
374-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
375                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
375-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
375-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
376            </intent-filter>
377            <intent-filter>
377-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
378                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
378-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
378-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
379            </intent-filter>
380            <intent-filter>
380-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
381                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
381-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
381-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
382            </intent-filter>
383        </receiver>
384
385        <service
385-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82b53e7ed6a17288256e97aa7a7e09b8\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
386            android:name="androidx.room.MultiInstanceInvalidationService"
386-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82b53e7ed6a17288256e97aa7a7e09b8\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
387            android:directBootAware="true"
387-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82b53e7ed6a17288256e97aa7a7e09b8\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
388            android:exported="false" />
388-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82b53e7ed6a17288256e97aa7a7e09b8\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
389        <service
389-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6623d766d878b79b1f5dea0a1a7f08b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
390            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
390-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6623d766d878b79b1f5dea0a1a7f08b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
391            android:exported="false" >
391-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6623d766d878b79b1f5dea0a1a7f08b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
392            <meta-data
392-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6623d766d878b79b1f5dea0a1a7f08b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
393                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
393-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6623d766d878b79b1f5dea0a1a7f08b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
394                android:value="cct" />
394-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6623d766d878b79b1f5dea0a1a7f08b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
395        </service>
396        <service
396-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5014b3ad63f7cceb01f18af1cfbd5a4e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
397            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
397-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5014b3ad63f7cceb01f18af1cfbd5a4e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
398            android:exported="false"
398-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5014b3ad63f7cceb01f18af1cfbd5a4e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
399            android:permission="android.permission.BIND_JOB_SERVICE" >
399-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5014b3ad63f7cceb01f18af1cfbd5a4e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
400        </service>
401
402        <receiver
402-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5014b3ad63f7cceb01f18af1cfbd5a4e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
403            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
403-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5014b3ad63f7cceb01f18af1cfbd5a4e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
404            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
404-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5014b3ad63f7cceb01f18af1cfbd5a4e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
405        <activity
405-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\832b1586368327240313f1a3abc0ec5d\transformed\core-common-2.0.2\AndroidManifest.xml:14:9-18:65
406            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
406-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\832b1586368327240313f1a3abc0ec5d\transformed\core-common-2.0.2\AndroidManifest.xml:15:13-93
407            android:exported="false"
407-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\832b1586368327240313f1a3abc0ec5d\transformed\core-common-2.0.2\AndroidManifest.xml:16:13-37
408            android:stateNotNeeded="true"
408-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\832b1586368327240313f1a3abc0ec5d\transformed\core-common-2.0.2\AndroidManifest.xml:17:13-42
409            android:theme="@style/Theme.PlayCore.Transparent" />
409-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\832b1586368327240313f1a3abc0ec5d\transformed\core-common-2.0.2\AndroidManifest.xml:18:13-62
410    </application>
411
412</manifest>
