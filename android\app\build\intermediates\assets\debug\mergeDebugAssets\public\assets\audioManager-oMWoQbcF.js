var x=Object.defineProperty;var I=(S,e,t)=>e in S?x(S,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):S[e]=t;var m=(S,e,t)=>I(S,typeof e!="symbol"?e+"":e,t);import{s as R,g as E,h as T,P as N,R as B,c as F,T as p,d as A}from"./audio-5UOY9KLB.js";const w={cardPlay:["/sounds/effects/card-place-1.ogg","/sounds/effects/card-place-2.ogg","/sounds/effects/card-place-3.ogg","/sounds/effects/card-place-4.ogg"],cardShuffle:["/sounds/effects/card-shuffle.ogg"],cardDeal:["/sounds/effects/card-slide-1.ogg","/sounds/effects/card-slide-2.ogg","/sounds/effects/card-slide-3.ogg","/sounds/effects/card-slide-4.ogg"],cardFlip:["/sounds/effects/card-slide-5.ogg","/sounds/effects/card-slide-6.ogg"],cardSnap:["/sounds/effects/card-shove-3.ogg","/sounds/effects/card-shove-4.ogg"],cardGather:["/sounds/effects/card-slide-7.ogg","/sounds/effects/card-slide-8.ogg"],cardFan:["/sounds/effects/card-fan-1.ogg","/sounds/effects/card-fan-2.ogg"],cardShove:["/sounds/effects/card-shove-1.ogg","/sounds/effects/card-shove-2.ogg"],cardPackTakeOut:["/sounds/effects/card-slide-1.ogg","/sounds/effects/card-slide-2.ogg","/sounds/effects/card-slide-3.ogg"],cardShuffleHandEnd:["/sounds/effects/card-shuffle.ogg"],buttonClick:["/sounds/effects/click.wav"],menuOpen:["/sounds/effects/click.wav"],menuClose:["/sounds/effects/click.wav"],turnStart:["/sounds/effects/card-slide-7.ogg"],gameStart:["/sounds/effects/card-shuffle.ogg"],gameEnd:["/sounds/effects/card-shuffle.ogg"],roundEnd:["/sounds/effects/card-shuffle.ogg"],victory:["/sounds/effects/card-place-1.ogg"],"victory-cheer":["/sounds/effects/card-place-1.ogg","/sounds/effects/card-place-2.ogg"],defeat:["/sounds/effects/card-shove-3.ogg"],"trump-select":["/sounds/effects/card-slide-8.ogg"],error:["/sounds/effects/card-shove-4.ogg"],success:["/sounds/effects/card-place-1.ogg"],warning:["/sounds/effects/card-slide-8.ogg"],notification:["/sounds/effects/card-slide-7.ogg"],maraffa:["/sounds/effects/maraffa.wav"],gameOver:["/sounds/effects/fine-partita.wav"]},z={menu:"/sounds/music/guitar-jazz.mp3",game:"/sounds/music/guitar-jazz.mp3",victory:"/sounds/music/guitar-jazz.mp3",intense:"/sounds/music/guitar-jazz.mp3",ambient:"/sounds/music/guitar-jazz.mp3"},D={cardPlay:{pitch:1,volume:.8},cardShuffle:{pitch:1,volume:.9},cardDeal:{pitch:1.1,volume:.7},cardFlip:{pitch:1.2,volume:.8},cardSnap:{pitch:.9,volume:.9},cardGather:{pitch:.8,volume:.7},cardFan:{pitch:.8,volume:.7},cardShove:{pitch:1,volume:.8},cardPackTakeOut:{pitch:1,volume:.8},cardShuffleHandEnd:{pitch:.9,volume:.9},buttonClick:{pitch:1,volume:.7},menuOpen:{pitch:1.2,volume:.8},menuClose:{pitch:.8,volume:.8},turnStart:{pitch:1.3,volume:.7},gameStart:{pitch:.8,volume:1},gameEnd:{pitch:.5,volume:.9},roundEnd:{pitch:.6,volume:.8},victory:{pitch:1.5,volume:1},"victory-cheer":{pitch:1.2,volume:1},defeat:{pitch:.7,volume:.9},"trump-select":{pitch:1.4,volume:.8},error:{pitch:.6,volume:.9},success:{pitch:2,volume:.8},warning:{pitch:1.6,volume:.7},notification:{pitch:1.8,volume:.6},maraffa:{pitch:1,volume:1},gameOver:{pitch:1,volume:.9}},P=S=>{const e=w[S];return e[Math.floor(Math.random()*e.length)]},O=S=>D[S]||{pitch:1,volume:.8},M=class M{constructor(){m(this,"audioContext",null);m(this,"isEnabled",!0);m(this,"soundEffectsEnabled",!0);m(this,"musicEnabled",!1);m(this,"masterVolume",.7);m(this,"soundEffectsVolume",.8);m(this,"musicVolume",.2);m(this,"currentMusic",null);m(this,"soundCache",new Map);m(this,"toneInitialized",!1);m(this,"activeSynth",null);m(this,"activePattern",null);m(this,"wasMusicPlaying",!1);m(this,"currentTrackName",null);m(this,"isAppInBackground",!1);m(this,"backgroundTimer",null);m(this,"musicSeekPosition",0);this.initializeAudioContext(),this.loadSoundSettings(),this.initializeTone()}static getInstance(){return M.instance||(M.instance=new M),M.instance}async initializeTone(){try{await R(),this.toneInitialized=!0}catch(e){console.warn("Failed to initialize Tone.js:",e)}}initializeAudioContext(){try{const e=window.AudioContext||window.webkitAudioContext;this.audioContext=new e}catch(e){console.warn("Audio context not supported:",e)}}async resumeAudioContext(){if(this.audioContext&&this.audioContext.state==="suspended")try{await this.audioContext.resume()}catch(e){console.warn("⚠️ Failed to resume AudioManager context:",e)}}loadSoundSettings(){try{const e=localStorage.getItem("audioSettings");if(e){const t=JSON.parse(e);this.isEnabled=t.isEnabled??!0,this.soundEffectsEnabled=t.soundEffectsEnabled??!0,this.musicEnabled=t.musicEnabled??!1,this.masterVolume=t.masterVolume??.7,this.soundEffectsVolume=t.soundEffectsVolume??.8,this.musicVolume=t.musicVolume??.2}}catch(e){console.warn("Error loading audio settings:",e)}}saveSoundSettings(){try{const e={isEnabled:this.isEnabled,soundEffectsEnabled:this.soundEffectsEnabled,musicEnabled:this.musicEnabled,masterVolume:this.masterVolume,soundEffectsVolume:this.soundEffectsVolume,musicVolume:this.musicVolume};localStorage.setItem("audioSettings",JSON.stringify(e))}catch(e){console.warn("Error saving audio settings:",e)}}setEnabled(e){this.isEnabled=e,!e&&this.currentMusic&&this.currentMusic.pause(),this.saveSoundSettings()}setSoundEffectsEnabled(e){this.soundEffectsEnabled=e,this.saveSoundSettings()}setMusicEnabled(e){this.musicEnabled=e,e||this.stopMusic(),this.saveSoundSettings()}isAudioEnabled(){return this.isEnabled}isSoundEffectsEnabled(){return this.isEnabled&&this.soundEffectsEnabled}isMusicEnabled(){return this.isEnabled&&this.musicEnabled}setMasterVolume(e){this.masterVolume=Math.max(0,Math.min(1,e)),this.updateAllVolumes(),this.saveSoundSettings()}setSoundEffectsVolume(e){this.soundEffectsVolume=Math.max(0,Math.min(1,e)),this.saveSoundSettings()}setMusicVolume(e){this.musicVolume=Math.max(0,Math.min(1,e)),this.currentMusic&&this.currentMusic.volume(this.masterVolume*this.musicVolume),this.saveSoundSettings()}updateAllVolumes(){this.currentMusic&&this.currentMusic.volume(this.masterVolume*this.musicVolume),this.activeSynth&&(this.activeSynth.volume.value=E(this.masterVolume*this.musicVolume*.15)),this.soundCache.forEach(e=>{e.volume(this.masterVolume*this.soundEffectsVolume)})}getMasterVolume(){return this.masterVolume}getSoundEffectsVolume(){return this.soundEffectsVolume}getMusicVolume(){return this.musicVolume}async playSound(e,t={}){if(this.isSoundEffectsEnabled())try{this.playKenneyCasinoSound(e,t)}catch(s){console.error("Error playing sound:",s),this.generateDirectAudio(e,t)}}getSoundFileForPlayer(e,t){if(e==="cardPlay"&&t!==void 0){const s=w.cardPlay,i=t%s.length;return s[i]}return P(e)}playKenneyCasinoSound(e,t={}){const s=this.getSoundFileForPlayer(e,t.playerId),i=O(e),c=(t.volume??i.volume??.8)*this.masterVolume*this.soundEffectsVolume,a=t.pitch??i.pitch??1;let r=this.soundCache.get(s);r?(r.volume(c),r.rate(a)):(r=new T.Howl({src:[s],volume:c,rate:a,preload:!0,onloaderror:(u,n)=>{console.error(`Failed to load sound file: ${s}`,n),this.audioContext&&this.generateDirectAudio(e,t)}}),this.soundCache.set(s,r)),r.play(),t.playerId!==void 0&&`${t.playerId}`}generateDirectAudio(e,t={}){if(this.audioContext)try{const s=(t.volume??1)*this.masterVolume*this.soundEffectsVolume;if(this.isCardSound(e)){this.generateRealisticCardSound(e,s,t.pitch);return}const i=this.audioContext,o=i.currentTime,c=i.createGain();c.gain.setValueAtTime(s,o);let a=440,r=.2,u="sine";switch(e){case"buttonClick":a=800,r=.1;break;case"menuOpen":a=523.25,r=.2,u="sweep";break;case"menuClose":a=392,r=.2,u="sweep";break;case"turnStart":a=698.46,r=.3,u="bell";break;case"gameStart":a=523.25,r=.5,u="fanfare";break;case"gameEnd":a=392,r=.8,u="chord";break;case"victory":a=659.25,r=1,u="chord";break;case"defeat":a=196,r=.8,u="chord";break;case"error":a=200,r=.3,u="noise";break;case"success":a=880,r=.4,u="ding";break;case"warning":a=440,r=.2,u="bell";break;case"notification":a=1046.5,r=.2,u="bell";break;case"roundEnd":a=523.25,r=.6,u="chord";break;case"victory-cheer":a=783.99,r=1.2,u="fanfare";break;case"trump-select":a=659.25,r=.3,u="ding";break;case"cardGather":a=440,r=.4,u="sweep";break}if(t.pitch&&(a*=t.pitch),u==="chord")[a,a*1.25,a*1.5].forEach(d=>{const h=i.createOscillator();h.frequency.setValueAtTime(d,o),h.type="sine",h.connect(c),h.start(o),h.stop(o+r)}),c.connect(i.destination);else if(u==="sweep"){const n=i.createOscillator();n.frequency.setValueAtTime(a,o),n.frequency.exponentialRampToValueAtTime(a*2,o+r),n.type="sine",n.connect(c),c.connect(i.destination),n.start(o),n.stop(o+r)}else if(u==="bell"){const n=i.createOscillator();n.frequency.setValueAtTime(a,o),n.frequency.exponentialRampToValueAtTime(a*.5,o+r),n.type="sine",n.connect(c),c.connect(i.destination),n.start(o),n.stop(o+r)}else if(u==="ding"){const n=i.createOscillator();n.frequency.setValueAtTime(a,o),n.type="triangle",n.connect(c),c.connect(i.destination),n.start(o),n.stop(o+r)}else if(u==="fanfare")[a,a*1.125,a*1.25,a*1.5].forEach((d,h)=>{const l=i.createOscillator(),f=o+h*.1;l.frequency.setValueAtTime(d,f),l.type="sawtooth";const g=i.createGain();g.gain.setValueAtTime(0,f),g.gain.linearRampToValueAtTime(s*.3,f+.05),g.gain.exponentialRampToValueAtTime(.001,f+.15),l.connect(g),g.connect(i.destination),l.start(f),l.stop(f+.15)});else if(u==="noise"){const n=i.sampleRate*r,d=i.createBuffer(1,n,i.sampleRate),h=d.getChannelData(0);for(let f=0;f<n;f++){const g=f/i.sampleRate,y=(Math.random()*2-1)*.3,v=Math.sin(g*Math.PI/r);h[f]=y*v}const l=i.createBufferSource();l.buffer=d,l.connect(c),c.connect(i.destination),l.start(o)}else{const n=i.createOscillator();n.frequency.setValueAtTime(a,o),n.type="sine",n.connect(c),c.connect(i.destination),n.start(o),n.stop(o+r)}}catch(s){console.error("Error playing direct audio:",s)}}isCardSound(e){return["cardPlay","cardShuffle","cardDeal","cardFlip","cardSnap"].includes(e)}generateRealisticCardSound(e,t,s){if(this.audioContext)try{switch(e){case"cardShuffle":this.generateRealisticShuffleSound(t);break;case"cardPlay":case"cardDeal":this.generateRealisticSlideSound(t,s);break;case"cardFlip":this.generateRealisticFlipSound(t,s);break;case"cardSnap":this.generateRealisticSnapSound(t,s);break}}catch(i){console.error("Error generating realistic card sound:",i)}}generateRealisticShuffleSound(e){if(!this.audioContext)return;const t=this.audioContext,s=t.currentTime;for(let i=0;i<8;i++){const o=s+i*.1,c=.3,a=t.sampleRate*c,r=t.createBuffer(1,a,t.sampleRate),u=r.getChannelData(0);for(let l=0;l<a;l++){const f=l/t.sampleRate,g=Math.random()*2-1,y=Math.sin(f*Math.PI*150)*.3,v=g*.7+y,b=Math.exp(-f*3)*Math.sin(f*Math.PI/c);u[l]=v*b*.4}const n=t.createBufferSource();n.buffer=r;const d=t.createBiquadFilter();d.type="bandpass",d.frequency.value=400+Math.random()*600,d.Q.value=2;const h=t.createGain();h.gain.setValueAtTime(0,o),h.gain.linearRampToValueAtTime(e*.6,o+.05),h.gain.exponentialRampToValueAtTime(.001,o+c-.05),n.connect(d),d.connect(h),h.connect(t.destination),n.start(o),n.stop(o+c)}}generateRealisticSlideSound(e,t){if(!this.audioContext)return;const s=this.audioContext,i=s.currentTime,o=.25,c=s.sampleRate*o,a=s.createBuffer(1,c,s.sampleRate),r=a.getChannelData(0);for(let l=0;l<c;l++){const f=l/s.sampleRate,g=f/o,y=(Math.random()*2-1)*.3,v=Math.sin(f*Math.PI*80)*.2,b=(Math.random()*2-1)*.1*Math.sin(f*Math.PI*1200),V=Math.exp(-g*2),C=Math.sin(g*Math.PI)*V;r[l]=(y+v+b)*C}const u=s.createBufferSource();u.buffer=a;const n=s.createBiquadFilter();n.type="lowpass",n.frequency.value=(t||1)*1500,n.Q.value=1;const d=s.createBiquadFilter();d.type="highpass",d.frequency.value=120,d.Q.value=.5;const h=s.createGain();h.gain.setValueAtTime(e*.8,i),u.connect(d),d.connect(n),n.connect(h),h.connect(s.destination),u.start(i),setTimeout(()=>{this.generateCardThud(e*.3)},o*800)}generateRealisticFlipSound(e,t){if(!this.audioContext)return;const s=this.audioContext,i=s.currentTime,o=.12,c=s.sampleRate*o,a=s.createBuffer(1,c,s.sampleRate),r=a.getChannelData(0);for(let h=0;h<c;h++){const l=h/s.sampleRate,f=l/o,g=(Math.random()*2-1)*.4,y=800+f*1200,v=Math.sin(l*Math.PI*y)*.3,b=Math.exp(-f*8)*Math.sin(f*Math.PI);r[h]=(g+v)*b}const u=s.createBufferSource();u.buffer=a;const n=s.createBiquadFilter();n.type="bandpass",n.frequency.value=(t||1)*1200,n.Q.value=3;const d=s.createGain();d.gain.setValueAtTime(e*.6,i),u.connect(n),n.connect(d),d.connect(s.destination),u.start(i)}generateRealisticSnapSound(e,t){if(!this.audioContext)return;const s=this.audioContext,i=s.currentTime,o=.08,c=s.sampleRate*o,a=s.createBuffer(1,c,s.sampleRate),r=a.getChannelData(0);for(let d=0;d<c;d++){const h=d/s.sampleRate,l=h/o,f=l<.1?Math.sin(l*Math.PI*50):0,g=Math.sin(h*Math.PI*2400*(t||1))*Math.exp(-l*15),y=(Math.random()*2-1)*.2*Math.exp(-l*20);r[d]=(f+g+y)*Math.exp(-l*12)}const u=s.createBufferSource();u.buffer=a;const n=s.createGain();n.gain.setValueAtTime(e*.9,i),u.connect(n),n.connect(s.destination),u.start(i)}generateCardThud(e){if(!this.audioContext)return;const t=this.audioContext,s=t.currentTime,i=.05,o=t.createOscillator();o.frequency.setValueAtTime(60,s),o.frequency.exponentialRampToValueAtTime(30,s+i),o.type="sine";const c=t.createGain();c.gain.setValueAtTime(e,s),c.gain.exponentialRampToValueAtTime(.001,s+i),o.connect(c),c.connect(t.destination),o.start(s),o.stop(s+i)}async playMusic(e,t=!0){var s;if(this.isMusicEnabled()&&!(this.currentTrackName===e&&((s=this.currentMusic)!=null&&s.playing())))try{this.currentTrackName=e,this.wasMusicPlaying=!0,this.currentMusic&&this.currentTrackName!==e&&(this.currentMusic.stop(),this.currentMusic=null),this.stopToneAudio();const i=z[e];if(!i){console.warn(`Nessun file musicale trovato per: ${e}, uso fallback generato`),this.generateBackgroundMusic(e,t);return}const o=this.masterVolume*this.musicVolume;this.currentMusic=new T.Howl({src:[i],loop:t,volume:o,preload:!0,onloaderror:(c,a)=>{this.generateBackgroundMusic(e,t)},onend:()=>{t||(this.currentMusic=null)}}),this.currentMusic.play()}catch(i){console.error("Error playing music:",i),this.generateBackgroundMusic(e,t)}}generateBackgroundMusic(e,t=!0){if(!this.toneInitialized){console.warn("Tone.js not initialized, cannot generate background music");return}if(!(this.currentTrackName===e&&this.activePattern&&this.activeSynth))try{this.activePattern&&this.currentTrackName!==e&&(this.activePattern.stop(),this.activePattern.dispose(),this.activePattern=null),this.activeSynth&&this.currentTrackName!==e&&(this.activeSynth.dispose(),this.activeSynth=null),this.currentTrackName=e;const s=new N().toDestination(),i=new B(4).toDestination();s.connect(i),this.activeSynth=s,s.volume.value=E(this.masterVolume*this.musicVolume*.15);const{chords:o,tempo:c}=this.getChordProgression(e),a=new F((r,u)=>{this.activeSynth&&!this.activeSynth.disposed&&s.triggerAttackRelease(u,"2n",r)},o);this.activePattern=a,a.interval="2n",p.bpm.value=c,t&&(a.iterations=1/0),a.start(),p.start(),this.currentMusic=new T.Howl({src:["data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+D0wWsiBDGH0fPTgjMGHm7A7+OZUR0PNHnE8NyJOggVX7zn6qhWFQlMn97yu2kiBjWH0fHTgTICHm3A7+OZURE6"],loop:!1,volume:0})}catch(s){console.error("Error generating background music:",s)}}getChordProgression(e){switch(e){case"menu":return{chords:[["C4","E4","G4"],["F4","A4","C5"],["G4","B4","D5"],["C4","E4","G4"]],tempo:70};case"game":return{chords:[["A3","C4","E4"],["F3","A3","C4"],["G3","B3","D4"],["A3","C4","E4"]],tempo:80};default:return{chords:[["D4","F#4","A4"],["G3","B3","D4"],["A3","C4","E4"],["D4","F#4","A4"]],tempo:65}}}stopMusic(){this.wasMusicPlaying=!1,this.currentTrackName=null,this.musicSeekPosition=0,this.currentMusic&&(this.currentMusic.stop(),this.currentMusic=null),this.stopToneAudio(),this.toneInitialized&&(p.stop(),p.cancel())}pauseMusic(){this.wasMusicPlaying=!!(this.currentMusic||this.activePattern),this.currentMusic&&this.currentMusic.pause(),this.toneInitialized&&p.pause()}resumeMusic(){if(!(!this.wasMusicPlaying||!this.isMusicEnabled())&&!(this.currentMusic&&this.currentMusic.playing())){if(this.currentMusic)try{this.currentMusic.play()}catch(e){console.warn("Error resuming music, restarting track:",e),this.currentTrackName&&this.playMusic(this.currentTrackName)}else this.currentTrackName&&this.playMusic(this.currentTrackName);if(this.toneInitialized&&p.state!=="started")try{p.start()}catch(e){console.warn("Error starting Tone.js transport:",e)}}}isTrackPlaying(e){return this.currentTrackName!==e?!1:!!(this.currentMusic&&this.currentMusic.playing()||this.activePattern&&this.activeSynth&&p.state==="started")}getCurrentTrackName(){return this.currentTrackName}preloadSounds(e){e.forEach(t=>{try{const s=P(t);if(!this.soundCache.has(s)){const i=new T.Howl({src:[s],preload:!0,volume:this.masterVolume*this.soundEffectsVolume,onloaderror:(o,c)=>{console.warn(`❌ Failed to preload: ${s}`,c)}});this.soundCache.set(s,i)}}catch(s){console.error(`Error preloading sound ${t}:`,s)}})}playGameStateSound(e){switch(e.gamePhase){case"setup":this.playSound("gameStart");break;case"play":this.playSound("turnStart");break;case"gameOver":this.playSound("gameEnd");break}}dispose(){this.stopMusic(),this.soundCache.clear(),this.backgroundTimer&&(clearTimeout(this.backgroundTimer),this.backgroundTimer=null),this.audioContext&&this.audioContext.state!=="closed"&&this.audioContext.close()}stopToneAudio(){this.activePattern&&(this.activePattern.stop(),this.activePattern.dispose(),this.activePattern=null),this.activeSynth&&(this.activeSynth.dispose(),this.activeSynth=null)}async stopAllAudio(){if(console.log("🔇 STOP ALL AUDIO - VERSIONE SUPER AGGRESSIVA"),this.stopMusic(),this.soundCache.forEach(e=>{try{e.playing()&&e.stop(),e.pause(),e.volume(0),e.mute(!0),(e._sounds||[]).forEach(s=>{if(s&&s._id)try{e.stop(s._id),e.pause(s._id)}catch{}})}catch{}}),this.toneInitialized)try{if(p.stop(),p.cancel(),this.stopToneAudio(),A.state==="running")try{await A.suspend()}catch{}if(A.state!=="closed"&&typeof A.close=="function")try{}catch{}}catch{}if(this.audioContext&&this.audioContext.state==="running")try{await this.audioContext.suspend()}catch{}this.currentTrackName=null,console.log("✅ STOP ALL AUDIO COMPLETATO")}async onAppBackground(){if(console.log("🔇 onAppBackground chiamato"),!this.isAndroid()){console.log("🌐 Web: ignoro background, utente controlla audio");return}this.backgroundTimer&&(clearTimeout(this.backgroundTimer),this.backgroundTimer=null),this.isAppInBackground=!0;const e=this.isTrackPlaying(this.currentTrackName||"");if(this.wasMusicPlaying=e,console.log(`🔇 Stato musica prima del background: playing=${e}, track=${this.currentTrackName}`),this.wasMusicPlaying&&this.currentMusic)try{const t=this.currentMusic.seek();this.musicSeekPosition=typeof t=="number"?t:0,console.log(`🔇 Posizione musica salvata: ${this.musicSeekPosition}`)}catch(t){console.error("❌ Errore nel salvare la posizione della musica:",t),this.musicSeekPosition=0}else this.musicSeekPosition=0;await this.stopAllAudio(),console.log("✅ Audio fermato completamente")}onAppForeground(){if(console.log("🔊 onAppForeground chiamato"),!this.isAndroid()){console.log("🌐 Web: ignoro foreground, utente controlla audio");return}this.isAppInBackground=!1,this.backgroundTimer&&clearTimeout(this.backgroundTimer),this.backgroundTimer=setTimeout(()=>{if(console.log(`🔊 Controllo resume: wasMusicPlaying=${this.wasMusicPlaying}, currentTrackName=${this.currentTrackName}, musicEnabled=${this.isMusicEnabled()}, seekPosition=${this.musicSeekPosition}`),!this.isAppInBackground&&this.wasMusicPlaying&&this.currentTrackName)if(console.log(`🔊 Riprendendo traccia: ${this.currentTrackName} dalla posizione ${this.musicSeekPosition}`),this.isMusicEnabled()){console.log("🔊 ANDROID: Riprendo musica immediatamente"),this.playMusic(this.currentTrackName);const e=()=>{if(this.currentMusic&&this.musicSeekPosition>0)try{const t=this.currentMusic.duration();t>0&&this.musicSeekPosition<t?(this.currentMusic.seek(this.musicSeekPosition),console.log(`✅ ANDROID: Musica ripresa da ${this.musicSeekPosition}s di ${t}s totali`)):console.warn(`⚠️ ANDROID: Posizione ${this.musicSeekPosition} non valida per durata ${t}`)}catch(t){console.error("❌ ANDROID: Errore nel seek della musica:",t)}this.musicSeekPosition=0};setTimeout(e,100),setTimeout(e,300),setTimeout(e,500)}else console.log("🔇 ANDROID: Musica era in riproduzione ma ora è disabilitata nelle impostazioni"),this.wasMusicPlaying=!1,this.musicSeekPosition=0;else console.log("🔇 Non riprendo la musica: condizioni non soddisfatte"),console.log(`  - isAppInBackground: ${this.isAppInBackground}`),console.log(`  - wasMusicPlaying: ${this.wasMusicPlaying}`),console.log(`  - currentTrackName: ${this.currentTrackName}`);this.backgroundTimer=null},300)}getIsAppInBackground(){return this.isAppInBackground}isAndroid(){try{const{Capacitor:e}=require("@capacitor/core");return e.isNativePlatform()&&e.getPlatform()==="android"}catch{return!1}}};m(M,"instance");let k=M;const $=k.getInstance();typeof window<"u"&&(window.AudioManager=k);export{k as A,$ as a};
