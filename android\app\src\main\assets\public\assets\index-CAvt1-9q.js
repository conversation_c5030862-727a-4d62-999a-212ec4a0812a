const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web-CcikGsaK.js","assets/index-CX90Qklj.js","assets/ui-Bu5Z5a0w.js","assets/vendor-COrNHRvO.js","assets/audio-5UOY9KLB.js","assets/supabase-DLIhrfaA.js","assets/audioManager-oMWoQbcF.js","assets/game-BIyZFdIB.js","assets/index-D4vqCpa7.css"])))=>i.map(i=>d[i]);
import{_ as o}from"./supabase-DLIhrfaA.js";import{r as t}from"./index-CX90Qklj.js";import"./vendor-COrNHRvO.js";import"./ui-Bu5Z5a0w.js";import"./audio-5UOY9KLB.js";import"./audioManager-oMWoQbcF.js";import"./game-BIyZFdIB.js";const n=t("Share",{web:()=>o(()=>import("./web-CcikGsaK.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8])).then(r=>new r.ShareWeb)});export{n as Share};
