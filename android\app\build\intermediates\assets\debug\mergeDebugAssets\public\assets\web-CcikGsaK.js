import{W as a}from"./index-CX90Qklj.js";import"./ui-Bu5Z5a0w.js";import"./vendor-COrNHRvO.js";import"./audio-5UOY9KLB.js";import"./supabase-DLIhrfaA.js";import"./audioManager-oMWoQbcF.js";import"./game-BIyZFdIB.js";class h extends a{async canShare(){return typeof navigator>"u"||!navigator.share?{value:!1}:{value:!0}}async share(e){if(typeof navigator>"u"||!navigator.share)throw this.unavailable("Share API not available in this browser");return await navigator.share({title:e.title,text:e.text,url:e.url}),{}}}export{h as ShareWeb};
