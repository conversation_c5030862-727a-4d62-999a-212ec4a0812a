#Fri Jul 18 16:32:51 CEST 2025
base.0=C\:\\Users\\zavat\\Desktop\\Progetti\\maraffa-romagnola\\android\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=C\:\\Users\\zavat\\Desktop\\Progetti\\maraffa-romagnola\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\0\\classes.dex
base.2=C\:\\Users\\zavat\\Desktop\\Progetti\\maraffa-romagnola\\android\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.3=C\:\\Users\\zavat\\Desktop\\Progetti\\maraffa-romagnola\\android\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\5\\classes.dex
base.4=C\:\\Users\\zavat\\Desktop\\Progetti\\maraffa-romagnola\\android\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
base.5=C\:\\Users\\zavat\\Desktop\\Progetti\\maraffa-romagnola\\android\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes3.dex
path.0=classes.dex
path.1=0/classes.dex
path.2=0/classes.dex
path.3=5/classes.dex
path.4=classes2.dex
path.5=classes3.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
