import { useState, useCallback, useMemo, useEffect } from "react";
import { Trophy, RefreshCcw, X } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import ActionButton from "@/components/ui/ActionButton";
import LeaderboardLoadingSkeleton from "./LeaderboardLoadingSkeleton";
import PlayerProfileModal from "./PlayerProfileModal";
import OptimizedScrollContainer from "@/components/ui/OptimizedScrollContainer";
import { getPlayerTitle } from "@/services/playerTitlesService";
import { useAuth, type LeaderboardPlayer } from "@/context/auth-context";
import { useAndroidScrollOptimization } from "@/hooks/useAndroidScrollOptimization";

interface LeaderboardProps {
  authUser: { id: string } | null;
  onAddFriend?: (playerId: string) => Promise<void>;
  onRemoveFriend?: (playerId: string) => Promise<void>;
  friendsList?: string[];
  hasPendingRequestFor?: (playerId: string) => boolean;
  hasReceivedRequestFrom?: (playerId: string) => boolean;
  onAcceptFriendRequest?: (playerId: string) => Promise<void>;
  onRejectFriendRequest?: (playerId: string) => Promise<void>;
}

const Leaderboard = ({
  authUser,
  onAddFriend,
  onRemoveFriend,
  friendsList = [],
  hasPendingRequestFor,
  hasReceivedRequestFrom,
  onAcceptFriendRequest,
  onRejectFriendRequest,
}: LeaderboardProps) => {
  const { getLeaderboard, invalidateLeaderboardCache } = useAuth();

  const [leaderboardType, setLeaderboardType] = useState<"level" | "wins">(
    "wins"
  );
  const [leaderboardPlayers, setLeaderboardPlayers] = useState<
    LeaderboardPlayer[]
  >([]);
  const [leaderboardLoading, setLeaderboardLoading] = useState(false);
  const [leaderboardError, setLeaderboardError] = useState<string | null>(null);
  const [leaderboardLoaded, setLeaderboardLoaded] = useState(false);
  const [page, setPage] = useState(0); // blocco corrente
  const [hasMore, setHasMore] = useState(true); // se ci sono altri risultati
  const [loadingMore, setLoadingMore] = useState(false); // caricamento pagine aggiuntive

  // Stati per la modale del profilo giocatore
  const [selectedPlayer, setSelectedPlayer] =
    useState<LeaderboardPlayer | null>(null);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);

  // Ordinamento della classifica con useMemo
  const sortedLeaderboard = useMemo(() => {
    return [...leaderboardPlayers]
      .sort((a, b) => {
        if (leaderboardType === "wins") {
          if (b.games_won !== a.games_won) {
            return b.games_won - a.games_won;
          }
          if (b.win_rate !== a.win_rate) {
            return b.win_rate - a.win_rate;
          }
          if (b.xp !== a.xp) {
            return b.xp - a.xp;
          }
        } else {
          if (b.xp !== a.xp) {
            return b.xp - a.xp;
          }
          if (b.level !== a.level) {
            return b.level - a.level;
          }
        }
        return a.username.localeCompare(b.username);
      })
      .map((player, index) => ({
        ...player,
        position: index + 1,
      }));
  }, [leaderboardPlayers, leaderboardType]);

  // Funzione per caricare la leaderboard
  const loadLeaderboard = useCallback(
    async (forceRefresh = false, nextPage = 0) => {
      if (leaderboardLoading) return;

      // Distingui tra caricamento iniziale e caricamento pagine aggiuntive
      if (nextPage === 0) {
        setLeaderboardLoading(true);
      } else {
        setLoadingMore(true);
      }
      setLeaderboardError(null);

      try {
        const LIMIT = 25;
        const offset = nextPage * LIMIT;
        console.log(
          `🏆 Caricamento leaderboard... pagina ${
            nextPage + 1
          } (offset ${offset})`,
          forceRefresh ? "(force refresh)" : ""
        );
        // Chiamata API con paginazione
        let data = [];
        try {
          data = await getLeaderboard(forceRefresh, offset, LIMIT);
        } catch (error) {
          // Fallback: carica tutti i dati e fai slice client-side
          console.warn(
            "API paginazione fallita, uso fallback client-side:",
            error
          );
          const allData = await getLeaderboard(forceRefresh);
          data = allData.slice(offset, offset + LIMIT);
        }
        if (forceRefresh || nextPage === 0) {
          setLeaderboardPlayers(data);
        } else {
          setLeaderboardPlayers((prev) => [...prev, ...data]);
        }
        setLeaderboardLoaded(true);
        // hasMore è true solo se abbiamo ricevuto esattamente LIMIT elementi
        // Se riceviamo meno di LIMIT, significa che non ci sono più dati
        setHasMore(data.length === LIMIT);
        setPage(nextPage);
        console.log(`✅ Leaderboard caricata: ${data.length} giocatori`);
      } catch (error) {
        console.error("❌ Errore caricamento leaderboard:", error);
        setLeaderboardError(
          "Errore nel caricamento della classifica. Riprova."
        );
      } finally {
        setLeaderboardLoading(false);
        setLoadingMore(false);
      }
    },
    [getLeaderboard, leaderboardLoading]
  );

  // Funzione per il refresh manuale
  const handleRefreshLeaderboard = useCallback(async () => {
    console.log("🏆 Refresh manuale leaderboard");
    invalidateLeaderboardCache();
    setPage(0);
    setHasMore(true);
    setLoadingMore(false);
    await loadLeaderboard(true, 0);
  }, [invalidateLeaderboardCache, loadLeaderboard]);

  // Funzioni per gestire la modale del profilo
  const handlePlayerClick = useCallback((player: LeaderboardPlayer) => {
    setSelectedPlayer(player);
    setIsProfileModalOpen(true);
  }, []);

  const handleCloseProfileModal = useCallback(() => {
    setIsProfileModalOpen(false);
    setSelectedPlayer(null);
  }, []);

  // Converte LeaderboardPlayer a PlayerStats per la modale
  const convertToPlayerStats = useCallback((player: LeaderboardPlayer) => {
    return {
      id: player.id,
      username: player.username,
      level: player.level,
      xp: player.xp,
      games_won: player.games_won,
      games_played: player.games_played,
      win_rate: player.win_rate,
      avatar_url: player.avatar_url,
      created_at: player.created_at,
      last_active: player.updated_at,
    };
  }, []);

  // Verifica se un giocatore è amico
  const isPlayerFriend = useCallback(
    (playerId: string) => {
      return friendsList.includes(playerId);
    },
    [friendsList]
  );

  // Carica la classifica al mount del componente
  useEffect(() => {
    if (!leaderboardLoaded) {
      console.log("🏆 Caricamento iniziale leaderboard");
      loadLeaderboard(false, 0);
    }
  }, [loadLeaderboard, leaderboardLoaded]);

  // Gestione ottimizzata del caricamento per infinite scroll
  const handleLoadMore = useCallback(() => {
    if (!leaderboardLoading && !loadingMore && hasMore) {
      loadLeaderboard(false, page + 1);
    }
  }, [leaderboardLoading, loadingMore, hasMore, page, loadLeaderboard]);

  return (
    <div className="h-full overflow-y-auto overscroll-contain">
      {/* Header */}
      <div className="from-yellow-500/20 to-red-500/20 pb-4">
        <Card className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-3">
                <img
                  src="/images/stremline-icons/Trophy--Streamline-Noto-Emojis.svg"
                  alt="Trophy"
                  className="h-6 w-6"
                />

                <h2
                  className="text-xl font-bold text-romagna-darkWood"
                  style={{
                    fontFamily: "'DynaPuff', cursive",
                    fontWeight: 500,
                  }}
                >
                  Classifica Globale
                </h2>
              </div>
              <ActionButton
                onClick={handleRefreshLeaderboard}
                disabled={leaderboardLoading}
                className={`p-2 rounded-lg transition-colors ${
                  leaderboardLoading
                    ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                    : "bg-amber-100 hover:bg-amber-200 text-amber-700"
                }`}
              >
                <RefreshCcw
                  className={`h-4 w-4 ${
                    leaderboardLoading ? "animate-spin" : ""
                  }`}
                />
              </ActionButton>
            </div>

            {/* Tab per tipo di classifica */}
            <div className="flex bg-white/70 rounded-lg p-1 border border-amber-200">
              <button
                onClick={() => {
                  setLeaderboardType("wins");
                  // Reset paginazione quando cambia tipo
                  setPage(0);
                  setHasMore(true);
                  setLoadingMore(false);
                }}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  leaderboardType === "wins"
                    ? "bg-amber-500 text-white shadow-sm"
                    : "text-amber-700 hover:bg-amber-100"
                }`}
              >
                <Trophy className="h-4 w-4 inline mr-2" />
                Per Vittorie
              </button>
              <button
                onClick={() => {
                  setLeaderboardType("level");
                  // Reset paginazione quando cambia tipo
                  setPage(0);
                  setHasMore(true);
                  setLoadingMore(false);
                }}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  leaderboardType === "level"
                    ? "bg-amber-500 text-white shadow-sm"
                    : "text-amber-700 hover:bg-amber-100"
                }`}
              >
                LV Per Livello
              </button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Content */}
      <div>
        {leaderboardLoading ? (
          <LeaderboardLoadingSkeleton
            message="Caricamento classifica..."
            count={5}
          />
        ) : leaderboardError ? (
          <Card className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm">
            <CardContent className="p-6 text-center">
              <p className="text-red-600 mb-4">{leaderboardError}</p>
              <Button
                onClick={handleRefreshLeaderboard}
                className="bg-romagna-rust hover:bg-romagna-terracotta text-white"
              >
                <RefreshCcw className="h-4 w-4 mr-2" />
                Riprova
              </Button>
            </CardContent>
          </Card>
        ) : leaderboardPlayers.length === 0 ? (
          <Card className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm">
            <CardContent className="p-6 text-center">
              <Trophy className="h-16 w-16 text-amber-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-romagna-darkWood mb-2">
                Nessun giocatore in classifica
              </h3>
              <p className="text-romagna-darkWood/70">
                Sii il primo a entrare in classifica giocando online!
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-3">
            {/* Podio per le prime 3 posizioni */}
            {sortedLeaderboard.filter(
              (player) => player.position && player.position <= 3
            ).length > 0 && (
              <Card className="relative border-2 border-amber-600/40 shadow-lg bg-gradient-to-br from-yellow-50/95 via-amber-50/95 to-orange-50/95 backdrop-blur-sm mb-3 overflow-hidden">
                {/* Sfondo animato CSS semplice */}
                <div className="absolute inset-0 opacity-40 bg-gradient-to-br from-yellow-300/60 via-amber-400/60 to-orange-300/60 animate-pulse"></div>
                <CardContent className="p-3 relative z-10">
                  <div className="flex items-end justify-center gap-4 px-4">
                    {/* Secondo posto */}
                    {sortedLeaderboard.find((p) => p.position === 2) && (
                      <div className="flex flex-col items-center">
                        <div className="bg-gray-200 rounded-t-lg w-24 h-18 flex items-end justify-center border-2 border-gray-300 relative">
                          <div className="relative">
                            <div
                              className="w-14 h-14 rounded-full bg-gradient-to-br from-amber-100 to-yellow-100 flex items-center justify-center border-2 border-amber-300 mb-2 cursor-pointer"
                              onClick={() =>
                                handlePlayerClick(
                                  sortedLeaderboard.find(
                                    (p) => p.position === 2
                                  )!
                                )
                              }
                            >
                              <img
                                src={
                                  getPlayerTitle(
                                    sortedLeaderboard.find(
                                      (p) => p.position === 2
                                    )!.level
                                  ).rankImage
                                }
                                alt="Rank"
                                className="w-10 h-10 object-contain"
                              />
                            </div>
                            <div className="absolute -bottom-1 -right-1 bg-gray-400 text-white rounded-full w-7 h-7 flex items-center justify-center text-sm font-bold shadow-md">
                              2
                            </div>
                          </div>
                        </div>
                        <p className="text-sm font-medium text-romagna-darkWood mt-2 text-center max-w-24">
                          {
                            sortedLeaderboard.find((p) => p.position === 2)!
                              .username
                          }
                        </p>
                        <p className="text-xs text-romagna-darkWood/70 text-center">
                          {leaderboardType === "level"
                            ? `${sortedLeaderboard
                                .find((p) => p.position === 2)!
                                .xp.toLocaleString()} XP`
                            : `${
                                sortedLeaderboard.find((p) => p.position === 2)!
                                  .games_won
                              } W`}
                        </p>
                      </div>
                    )}

                    {/* Primo posto */}
                    {sortedLeaderboard.find((p) => p.position === 1) && (
                      <div className="flex flex-col items-center">
                        <div className="bg-yellow-300 rounded-t-lg w-28 h-24 flex items-end justify-center border-2 border-yellow-400 relative">
                          <div className="relative">
                            <div
                              className="w-16 h-16 rounded-full bg-gradient-to-br from-amber-100 to-yellow-100 flex items-center justify-center border-2 border-amber-300 mb-2 cursor-pointer"
                              onClick={() =>
                                handlePlayerClick(
                                  sortedLeaderboard.find(
                                    (p) => p.position === 1
                                  )!
                                )
                              }
                            >
                              <img
                                src={
                                  getPlayerTitle(
                                    sortedLeaderboard.find(
                                      (p) => p.position === 1
                                    )!.level
                                  ).rankImage
                                }
                                alt="Rank"
                                className="w-12 h-12 object-contain"
                              />
                            </div>
                            <div className="absolute -bottom-1 -right-1 bg-yellow-500 text-white rounded-full w-9 h-9 flex items-center justify-center text-sm font-bold shadow-md">
                              <img
                                src="/images/stremline-icons/crown.png"
                                alt="Crown"
                                className="h-5 w-5"
                              />
                            </div>
                          </div>
                        </div>
                        <p className="text-sm font-semibold text-romagna-darkWood mt-2 text-center max-w-28">
                          {
                            sortedLeaderboard.find((p) => p.position === 1)!
                              .username
                          }
                        </p>
                        <p className="text-sm text-romagna-darkWood/70 text-center font-medium">
                          {leaderboardType === "level"
                            ? `${sortedLeaderboard
                                .find((p) => p.position === 1)!
                                .xp.toLocaleString()} XP`
                            : `${
                                sortedLeaderboard.find((p) => p.position === 1)!
                                  .games_won
                              } Vittorie`}
                        </p>
                      </div>
                    )}

                    {/* Terzo posto */}
                    {sortedLeaderboard.find((p) => p.position === 3) && (
                      <div className="flex flex-col items-center">
                        <div className="bg-amber-600 rounded-t-lg w-20 h-14 flex items-end justify-center border-2 border-amber-700 relative">
                          <div className="relative">
                            <div
                              className="w-12 h-12 rounded-full bg-gradient-to-br from-amber-100 to-yellow-100 flex items-center justify-center border-2 border-amber-300 mb-1 cursor-pointer"
                              onClick={() =>
                                handlePlayerClick(
                                  sortedLeaderboard.find(
                                    (p) => p.position === 3
                                  )!
                                )
                              }
                            >
                              <img
                                src={
                                  getPlayerTitle(
                                    sortedLeaderboard.find(
                                      (p) => p.position === 3
                                    )!.level
                                  ).rankImage
                                }
                                alt="Rank"
                                className="w-8 h-8 object-contain"
                              />
                            </div>
                            <div className="absolute -bottom-0.5 -right-0.5 bg-amber-700 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold shadow-md">
                              3
                            </div>
                          </div>
                        </div>
                        <p className="text-sm font-medium text-romagna-darkWood mt-2 text-center max-w-20">
                          {
                            sortedLeaderboard.find((p) => p.position === 3)!
                              .username
                          }
                        </p>
                        <p className="text-xs text-romagna-darkWood/70 text-center">
                          {leaderboardType === "level"
                            ? `${sortedLeaderboard
                                .find((p) => p.position === 3)!
                                .xp.toLocaleString()} XP`
                            : `${
                                sortedLeaderboard.find((p) => p.position === 3)!
                                  .games_won
                              } W`}
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Lista normale per le posizioni dalla 4 in poi */}
            {sortedLeaderboard
              .filter((player) => !player.position || player.position > 3)
              .map((player) => {
                const isCurrentUser = player.id === authUser?.id;
                return (
                  <Card
                    key={player.id}
                    onClick={() => handlePlayerClick(player)}
                    className={`border-2 shadow-md backdrop-blur-sm cursor-pointer transition-all duration-200 hover:scale-[1.01] hover:shadow-lg mx-1 ${
                      isCurrentUser
                        ? "border-blue-600/50 bg-gradient-to-r from-blue-50/90 to-indigo-50/90 hover:from-blue-100/90 hover:to-indigo-100/90"
                        : "border-amber-800/30 bg-amber-50/80 hover:bg-amber-100/80"
                    }`}
                  >
                    <CardContent className="p-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {/* Posizione */}
                          <div className="w-8 h-8 flex items-center justify-center">
                            {player.position === 1 ? (
                              <span className="text-2xl">🥇</span>
                            ) : player.position === 2 ? (
                              <span className="text-2xl">🥈</span>
                            ) : player.position === 3 ? (
                              <span className="text-2xl">🥉</span>
                            ) : (
                              <div
                                className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm ${
                                  isCurrentUser
                                    ? "bg-blue-400 text-white"
                                    : "bg-amber-200 text-amber-800"
                                }`}
                              >
                                {player.position}
                              </div>
                            )}
                          </div>
                          {/* Info utente */}
                          <div className="flex items-center gap-1">
                            {/* Rank image rimossa per posizioni > 3 */}
                            <div>
                              <p
                                className={`text-md font-semibold ${
                                  isCurrentUser
                                    ? "text-blue-700"
                                    : "text-romagna-darkWood"
                                }`}
                              >
                                {player.username}
                              </p>
                              <p className="text-xs text-romagna-darkWood/70">
                                {leaderboardType === "level"
                                  ? `${player.xp.toLocaleString()} XP • Livello ${
                                      player.level
                                    }`
                                  : `${
                                      player.games_won
                                    } vittorie • ${player.win_rate.toFixed(
                                      1
                                    )}% tasso vittoria`}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
          </div>
        )}

        {/* Indicatore caricamento pagine aggiuntive */}
        {loadingMore && (
          <div className="flex justify-center py-4">
            <div className="flex items-center gap-2 text-romagna-darkWood/70">
              <div className="animate-spin h-4 w-4 border-2 border-amber-600 border-t-transparent rounded-full" />
              <span className="text-sm">Caricamento altri risultati...</span>
            </div>
          </div>
        )}
      </div>

      {/* Modale profilo giocatore */}
      <PlayerProfileModal
        player={selectedPlayer ? convertToPlayerStats(selectedPlayer) : null}
        isOpen={isProfileModalOpen}
        onClose={handleCloseProfileModal}
        currentUserId={authUser?.id}
        isFriend={selectedPlayer ? isPlayerFriend(selectedPlayer.id) : false}
        hasPendingRequest={
          selectedPlayer && hasPendingRequestFor
            ? hasPendingRequestFor(selectedPlayer.id)
            : false
        }
        hasReceivedRequest={
          selectedPlayer && hasReceivedRequestFrom
            ? hasReceivedRequestFrom(selectedPlayer.id)
            : false
        }
        onAddFriend={onAddFriend}
        onRemoveFriend={onRemoveFriend}
        onAcceptFriendRequest={onAcceptFriendRequest}
        onRejectFriendRequest={onRejectFriendRequest}
      />

      {/* Bottone Carica Altri */}
      {hasMore && !leaderboardLoading && (
        <div className="p-4 flex justify-center">
          <button
            onClick={handleLoadMore}
            disabled={loadingMore}
            className="px-4 py-2 bg-gradient-to-r from-amber-500 to-orange-500 text-white font-bold rounded-lg shadow-md hover:shadow-lg hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            {loadingMore ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Caricamento...
              </div>
            ) : (
              `Carica altri`
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default Leaderboard;
