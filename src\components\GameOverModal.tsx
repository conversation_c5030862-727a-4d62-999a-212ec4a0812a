import React, { useEffect, useState } from "react";
import { AdMob } from "@capacitor-community/admob";
import { Capacitor } from "@capacitor/core";
import { useAudio } from "@/hooks/useAudio";

import {
  recordGameCompleted,
  shouldShowReviewRequest,
  recordReviewRequestShown,
  showNativeReviewModal,
} from "@/services/reviewTrackingService";
import AnalyticsService from "@/services/analyticsService";

import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Crown, Star, Play, Home, Zap } from "lucide-react";
import ActionButton from "./ui/ActionButton";
import confetti from "canvas-confetti";
import { GameState } from "@/utils/game/gameLogic";
import {
  updateStatsAfterGame,
  getProgressionInfo,
  type UnifiedStats,
} from "@/services/statsManager";
import {
  isPerfectGame,
  isComeback,
  getPlayerMaraffeMade,
} from "@/utils/game/gameAnalysis";

interface GameOverModalProps {
  isOpen: boolean;
  gameState: GameState;
  onStartNewGame: () => void;
  onReturnToMenu: () => void;
  isOnlineMode?: boolean;
  difficulty?: "easy" | "medium" | "hard";
}

const GameOverModal: React.FC<GameOverModalProps> = ({
  isOpen,
  gameState,
  onStartNewGame,
  onReturnToMenu,
  isOnlineMode = false,
  difficulty = "medium",
}) => {
  const [showAnimation, setShowAnimation] = useState(false);
  const [xpGained, setXpGained] = useState(0);
  const [xpBreakdown, setXpBreakdown] = useState<string[]>([]);
  const [leveledUp, setLeveledUp] = useState(false);
  const [newLevel, setNewLevel] = useState(0);
  const [xpProgress, setXpProgress] = useState({
    current: 0,
    total: 0,
    percentage: 0,
  });
  const [xpProcessed, setXpProcessed] = useState(false);
  const [showLevelUpMessage, setShowLevelUpMessage] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [rewardAdWatched, setRewardAdWatched] = useState(false);
  const [isLoadingRewardAd, setIsLoadingRewardAd] = useState(false);
  const [interstitialAdShown, setInterstitialAdShown] = useState(false);

  const { playSound, playMusic, stopMusic, isTrackPlaying } = useAudio();
  // Determina il team vincitore
  const winnerTeam = gameState.gameScore[0] > gameState.gameScore[1] ? 0 : 1;

  // Calcola se è stata una vittoria dominante (differenza > 10 punti)
  const isDominantWin =
    Math.abs(gameState.gameScore[0] - gameState.gameScore[1]) > 10;
  // Determina se il giocatore ha vinto (Team 1 = giocatore e compagno)
  const playerWon = winnerTeam === 0;
  useEffect(() => {
    if (isOpen && !xpProcessed) {
      const processGameStats = async () => {
        try {
          console.log("🎯 GameOverModal: Inizio processamento statistiche...");

          // Calcola i parametri aggiuntivi per l'XP
          const maraffeMade = getPlayerMaraffeMade(gameState);
          const perfectGame = isPerfectGame(gameState, winnerTeam);
          const comeback = isComeback(gameState, winnerTeam);
          const dominantWin = isDominantWin;

          console.log("🎯 GameOverModal: Parametri partita:", {
            isWinner: playerWon,
            difficulty,
            playerTeam: 0,
            finalScore: [gameState.gameScore[0], gameState.gameScore[1]],
            maraffeMade,
            isPerfectGame: perfectGame,
            isComeback: comeback,
            isDominantWin: dominantWin,
            isAbandoned: false,
          });

          // Aggiorna le statistiche con retry automatico
          let result: {
            stats: UnifiedStats;
            leveledUp: boolean;
            xpGained: number;
            xpBreakdown: string[];
          } | null = null;
          let retryCount = 0;
          const maxRetries = 3;

          while (retryCount < maxRetries) {
            try {
              result = updateStatsAfterGame({
                isWinner: playerWon,
                difficulty,
                maraffeMade,
                isPerfectGame: perfectGame,
                isComeback: comeback,
                isDominantWin: dominantWin,
                isAbandoned: false,
              });
              break; // Successo, esci dal loop
            } catch (error) {
              retryCount++;
              console.error(`❌ Tentativo ${retryCount} fallito:`, error);
              if (retryCount < maxRetries) {
                await new Promise((resolve) =>
                  setTimeout(resolve, 1000 * retryCount)
                );
              }
            }
          }

          if (!result) {
            throw new Error(
              "Tutti i tentativi di aggiornamento statistiche falliti"
            );
          }

          console.log(
            "🎯 GameOverModal: Risultato aggiornamento statistiche:",
            result
          );

          console.log("🎯 GameOverModal: Stato utente aggiornato con successo");

          setXpGained(result.xpGained);
          setXpBreakdown(result.xpBreakdown);
          setLeveledUp(result.leveledUp);
          setNewLevel(result.stats.level);

          // Calcola la progressione XP per la barra usando il sistema unificato
          const progressionInfo = getProgressionInfo(result.stats);
          setXpProgress({
            current: progressionInfo.progressXp,
            total: progressionInfo.xpForNextLevel,
            percentage: progressionInfo.progressPercentage,
          });

          setXpProcessed(true);

          // 📝 TRACKING RECENSIONI - Registra partita completata
          recordGameCompleted();

          // 📊 ANALYTICS - Traccia fine partita
          try {
            const gameDuration =
              Date.now() - (gameState.gameStartTime || Date.now());
            await AnalyticsService.trackGameEnd(
              difficulty,
              playerWon,
              gameDuration,
              [gameState.gameScore[0], gameState.gameScore[1]],
              maraffeMade
            );

            // Traccia level up se è avvenuto
            if (result.leveledUp) {
              await AnalyticsService.trackLevelUp(
                result.stats.level,
                result.xpGained
              );
            }
          } catch (error) {
            console.warn("⚠️ Errore tracking analytics:", error);
          }
        } catch (error) {
          console.error(
            "❌ GameOverModal: Errore processamento statistiche:",
            error
          );
          // Imposta valori di fallback
          setXpGained(0);
          setXpBreakdown(["Errore nell'aggiornamento"]);
          setLeveledUp(false);
          setNewLevel(1);
          setXpProgress({ current: 0, total: 100, percentage: 0 });
          setXpProcessed(true);

          // 📝 TRACKING RECENSIONI - Registra partita anche in caso di errore
          recordGameCompleted();
        }
      };

      processGameStats();

      // 🔊 Play game over sound first
      playSound("gameOver");

      // Play victory or defeat sound and music
      if (playerWon) {
        playSound("victory-cheer");
        // Solo avvia la musica di vittoria se non è già in riproduzione
        if (!isTrackPlaying("victory")) {
          playMusic("victory");
        }
      } else {
        playSound("defeat");
        // Don't play defeat music, just stop current music
        stopMusic();
      }

      // Trigger animation after modal opens
      const timer = setTimeout(() => {
        setShowAnimation(true);

        // Only trigger confetti for player victory
        if (playerWon) {
          confetti({
            particleCount: 150,
            spread: 80,
            origin: { y: 0.5 },
            colors: ["#FFD700", "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4"],
          });

          // Second wave of confetti
          setTimeout(() => {
            confetti({
              particleCount: 100,
              spread: 60,
              origin: { y: 0.7 },
              colors: ["#FFD700", "#FF6B6B"],
            });
          }, 300);
        }
      }, 500);

      // 🎬 PUBBLICITÀ AUTOMATICA DOPO PARTITA
      const adTimer = setTimeout(async () => {
        console.log("🎬 Timer pubblicità scattato...");
        console.log("🎬 Platform:", Capacitor.getPlatform());
        console.log("🎬 interstitialAdShown:", interstitialAdShown);

        if (Capacitor.getPlatform() !== "web" && !interstitialAdShown) {
          try {
            console.log("🎬 AVVIO pubblicità automatica dopo partita...");
            setInterstitialAdShown(true); // Marca come mostrato per evitare duplicati

            // Inizializza AdMob
            console.log("🎬 Inizializzazione AdMob...");
            await AdMob.initialize();

            // 🎯 PROVA PRIMA L'ID INTERSTITIAL, POI FALLBACK AL REWARD VIDEO
            let adShown = false;

            // Tentativo 1: Interstitial Ad
            try {
              console.log(
                "🎬 Preparazione interstitial con ID: ca-app-pub-3013811216506035/1024211513"
              );
              await AdMob.prepareInterstitial({
                adId: "ca-app-pub-3013811216506035/1024211513",
                isTesting: false,
              });

              console.log("🎬 Mostrando interstitial...");
              await AdMob.showInterstitial();
              console.log("✅ Pubblicità interstitial mostrata con successo");
              adShown = true;
            } catch (interstitialError) {
              console.warn(
                "⚠️ Interstitial fallito, provo reward video:",
                interstitialError
              );

              // Tentativo 2: Reward Video Ad (senza premio)
              try {
                console.log("🎬 Fallback: Preparazione reward video...");
                await AdMob.prepareRewardVideoAd({
                  adId: "ca-app-pub-3013811216506035/8135187414",
                  isTesting: false,
                });

                console.log("🎬 Mostrando reward video...");
                await AdMob.showRewardVideoAd();
                console.log(
                  "✅ Pubblicità reward video mostrata con successo (fallback)"
                );
                adShown = true;
              } catch (rewardError) {
                console.error("❌ Anche reward video fallito:", rewardError);
              }
            }

            if (!adShown) {
              console.warn("⚠️ Nessuna pubblicità disponibile");
            }
          } catch (e) {
            console.error("❌ Errore pubblicità automatica:", e);
          }
        } else {
          console.log("🎬 Pubblicità saltata - web o già mostrata");
        }
      }, 500);

      return () => {
        clearTimeout(timer);
        clearTimeout(adTimer);
      };
    }

    // Se la modale è aperta ma l'XP è già stato processato, mostra comunque l'animazione
    if (isOpen && xpProcessed && !showAnimation) {
      const timer = setTimeout(() => {
        setShowAnimation(true);
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [
    isOpen,
    xpProcessed,
    showAnimation,
    playerWon,
    playSound,
    playMusic,
    stopMusic,
    isTrackPlaying,
    isOnlineMode,
    difficulty,
    gameState,
    winnerTeam,
    isDominantWin,
    interstitialAdShown,
  ]);
  // Reset dello stato quando la modale si chiude
  useEffect(() => {
    if (!isOpen) {
      setXpProcessed(false);
      setShowAnimation(false);
      setShowLevelUpMessage(true);
      setIsTransitioning(false);
      setRewardAdWatched(false);
      setIsLoadingRewardAd(false);
      setInterstitialAdShown(false); // Reset per permettere pubblicità nella prossima partita
    }
  }, [isOpen]); // Alternanza tra messaggio level up e XP guadagnati con animazione
  useEffect(() => {
    if (isOpen && leveledUp && showAnimation) {
      const interval = setInterval(() => {
        // Inizia l'animazione di fade-out
        setIsTransitioning(true);

        // Dopo 300ms (durata fade-out), cambia il messaggio e fade-in
        setTimeout(() => {
          setShowLevelUpMessage((prev) => !prev);
          setIsTransitioning(false);
        }, 300);
      }, 3000); // Cambia ogni 3 secondi per una transizione più fluida

      return () => clearInterval(interval);
    }
  }, [isOpen, leveledUp, showAnimation]);

  const getWinnerTitle = () => {
    if (playerWon) {
      if (isDominantWin) {
        return "🏆 VITTORIA DOMINANTE! 🏆";
      }
      return "🏆 PARTITA VINTA! 🏆";
    } else {
      if (isDominantWin) {
        return " SCONFITTA TOTALE ";
      }
      return " PARTITA PERSA";
    }
  };
  const getTeamName = (teamId: number) => {
    return teamId === 0 ? "Il tuo team" : "Team avversario";
  };

  // Funzione per gestire l'annuncio con premio per XP doppi
  const handleRewardedAd = async () => {
    if (rewardAdWatched || isLoadingRewardAd || !Capacitor.isNativePlatform()) {
      return;
    }

    setIsLoadingRewardAd(true);

    try {
      console.log("🎬 Inizializzazione annuncio con premio per XP doppi...");

      // Prepara l'annuncio con premio
      await AdMob.prepareRewardVideoAd({
        adId: "ca-app-pub-3013811216506035/8135187414",
        isTesting: false, // Cambia in true per testing
      });

      console.log("🎬 Annuncio preparato, mostrando...");

      // Mostra l'annuncio
      const result = await AdMob.showRewardVideoAd();

      console.log("🎬 Risultato annuncio con premio:", result);

      // Se l'annuncio è stato guardato completamente
      if (result && result.type && result.amount) {
        console.log(
          "🎉 Annuncio guardato completamente! Assegnando XP doppi..."
        );

        // Marca come guardato
        setRewardAdWatched(true);

        // Raddoppia gli XP (aggiorna il display locale)
        const doubledXp = xpGained * 2;
        const additionalXp = xpGained; // XP aggiuntivi da aggiungere
        setXpGained(doubledXp);

        // Aggiorna SOLO gli XP senza registrare una nuova partita usando il sistema unificato
        const { loadStats, saveStats } = await import(
          "@/services/statsManager"
        );

        const currentStats = loadStats();
        const newXp = currentStats.xp + additionalXp;

        // Salva solo XP aggiornati (il livello viene calcolato automaticamente)
        const updatedStats = saveStats({
          xp: newXp,
        });

        console.log(
          `✅ XP aggiornati: ${currentStats.xp} + ${additionalXp} = ${newXp} (Livello: ${updatedStats.level})`
        );

        // Aggiorna gli stati locali per riflettere i nuovi valori nella UI
        if (updatedStats.level > currentStats.level) {
          setLeveledUp(true);
          setNewLevel(updatedStats.level);
        }

        // Forza il re-render dell'animazione XP con i nuovi valori
        setShowAnimation(false);
        setTimeout(() => {
          setShowAnimation(true);
        }, 100);

        // Mostra feedback positivo
        playSound("victory-cheer");

        // Confetti extra per il reward
        confetti({
          particleCount: 200,
          spread: 90,
          origin: { y: 0.6 },
          colors: [
            "#FFD700",
            "#FF6B6B",
            "#4ECDC4",
            "#45B7D1",
            "#96CEB4",
            "#9D4EDD",
          ],
        });

        console.log(`✅ XP raddoppiati da ${xpGained / 2} a ${doubledXp}!`);
      } else {
        console.log("❌ Annuncio non completato o non premiato");
      }
    } catch (error) {
      console.error(
        "❌ Errore durante la visualizzazione dell'annuncio con premio:",
        error
      );
    } finally {
      setIsLoadingRewardAd(false);
    }
  };
  return (
    <>
      <Dialog open={isOpen} onOpenChange={() => {}}>
        <DialogContent className="border-0 p-0 overflow-hidden shadow-2xl max-w-lg rounded-3xl [&>button]:hidden max-h-[90vh] flex flex-col">
          {/* Header compatto con gradiente del team vincitore */}
          <div
            className={`relative text-white p-4 rounded-t-3xl ${
              winnerTeam === 0
                ? "bg-gradient-to-r from-amber-500 to-yellow-500"
                : "bg-gradient-to-r from-red-500 to-rose-500"
            }`}
          >
            {/* Titolo compatto */}
            <DialogTitle className="text-xl font-bold text-center drop-shadow-lg">
              {showAnimation && (
                <span className="animate-fade-in text-white">
                  {getWinnerTitle()}
                </span>
              )}
            </DialogTitle>
          </div>
          {/* Corpo della modale con scroll */}
          <div className="bg-white p-6 rounded-b-3xl overflow-y-auto flex-1">
            {/* Punteggi finali con design migliorato */}
            <div className="space-y-4 mb-6">
              {showAnimation && (
                <div className="animate-fade-in delay-100">
                  {/* Griglia punteggi con design premium */}
                  <div className="grid grid-cols-2 gap-3">
                    {/* Il tuo team (Team 0) */}
                    <div
                      className={`relative p-3 rounded-2xl border-2 text-center transition-all duration-300 ${
                        winnerTeam === 0
                          ? "bg-gradient-to-br from-amber-50 to-yellow-50 border-amber-300 shadow-lg shadow-amber-200/50"
                          : "bg-gradient-to-br from-gray-50 to-gray-100 border-gray-200 shadow-sm"
                      }`}
                    >
                      {/* Badge vincitore */}
                      {winnerTeam === 0 && (
                        <div className="absolute -top-2 -right-2 bg-amber-400 rounded-full p-1 shadow-lg">
                          <Crown
                            className="h-4 w-4 text-white"
                            fill="currentColor"
                          />
                        </div>
                      )}

                      <div className="space-y-1">
                        <h4
                          className={`font-bold text-base ${
                            winnerTeam === 0
                              ? "text-amber-700"
                              : "text-gray-600"
                          }`}
                        >
                          {getTeamName(0)}
                        </h4>
                        <p
                          className={`text-3xl font-bold ${
                            winnerTeam === 0
                              ? "text-amber-600"
                              : "text-gray-500"
                          }`}
                        >
                          {gameState.gameScore[0]}
                        </p>
                        <p className="text-xs text-gray-400 uppercase tracking-wide">
                          punti
                        </p>
                      </div>
                    </div>

                    {/* Team avversario (Team 1) */}
                    <div
                      className={`relative p-3 rounded-2xl border-2 text-center transition-all duration-300 ${
                        winnerTeam === 1
                          ? "bg-gradient-to-br from-red-50 to-rose-50 border-red-300 shadow-lg shadow-red-200/50"
                          : "bg-gradient-to-br from-gray-50 to-gray-100 border-gray-200 shadow-sm"
                      }`}
                    >
                      {/* Badge vincitore */}
                      {winnerTeam === 1 && (
                        <div className="absolute -top-2 -right-2 bg-red-400 rounded-full p-1 shadow-lg">
                          <Crown
                            className="h-4 w-4 text-white"
                            fill="currentColor"
                          />
                        </div>
                      )}

                      <div className="space-y-1">
                        <h4
                          className={`font-bold text-base ${
                            winnerTeam === 1 ? "text-red-700" : "text-gray-600"
                          }`}
                        >
                          {getTeamName(1)}
                        </h4>
                        <p
                          className={`text-3xl font-bold ${
                            winnerTeam === 1 ? "text-red-600" : "text-gray-500"
                          }`}
                        >
                          {gameState.gameScore[1]}
                        </p>
                        <p className="text-xs text-gray-400 uppercase tracking-wide">
                          punti
                        </p>
                      </div>
                    </div>
                  </div>
                  {/* Esperienza e Progressione */}
                  <div className="mt-4 space-y-4">
                    {!isOnlineMode && (
                      <div className="space-y-4">
                        {/* Messaggio Level Up */}
                        {/* {leveledUp && (
                        <div className="p-4 bg-gradient-to-r from-yellow-50 to-amber-50 rounded-xl border-2 border-yellow-300 shadow-lg">
                          <div className="text-center">
                            <div className="flex items-center justify-center gap-2 mb-2">
                              <Zap className="h-6 w-6 text-yellow-600 animate-pulse" />
                              <p className="text-xl text-yellow-800 font-bold">
                                🎉 NUOVO LIVELLO RAGGIUNTO! 🎉
                              </p>
                            </div>
                            <p className="text-lg text-yellow-700 font-semibold">
                              Livello {newLevel} sbloccato!
                            </p>
                          </div>
                        </div>
                      )} */}{" "}
                        {/* Barra dell'Esperienza */}
                        <div className="p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl border-2 border-purple-200/50 shadow-sm">
                          <div className="space-y-3">
                            {/* Header XP - Alternanza tra Level Up e XP Guadagnati */}
                            <div className="flex items-center justify-between">
                              {" "}
                              <div className="flex items-center gap-2">
                                {leveledUp ? (
                                  <div
                                    className={`transition-all duration-300 ease-in-out transform ${
                                      isTransitioning
                                        ? "opacity-0 scale-95 translate-y-2"
                                        : "opacity-100 scale-100 translate-y-0"
                                    }`}
                                  >
                                    {showLevelUpMessage ? (
                                      <div className="flex items-center gap-2">
                                        <Zap className="h-5 w-5 text-yellow-600 animate-pulse" />
                                        <span className="text-lg text-yellow-800 font-bold">
                                          🎉 NUOVO LIVELLO {newLevel}! 🎉
                                        </span>
                                      </div>
                                    ) : (
                                      <div className="flex items-center gap-2">
                                        <Star className="h-5 w-5 text-purple-600" />
                                        <span className="text-lg text-purple-800 font-bold">
                                          +{xpGained} XP Guadagnati!
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                ) : (
                                  <div className="flex items-center gap-2">
                                    <Star className="h-5 w-5 text-purple-600" />
                                    <span className="text-lg text-purple-800 font-bold">
                                      +{xpGained} XP Guadagnati!
                                    </span>
                                  </div>
                                )}
                              </div>
                              <span className="text-sm text-purple-600 font-medium">
                                Livello {newLevel}
                              </span>
                            </div>

                            {/* Barra di Progressione */}
                            <div className="space-y-2">
                              <div className="w-full bg-purple-200 rounded-full h-3 overflow-hidden">
                                <div
                                  className="h-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full transition-all duration-2000 ease-out"
                                  style={{
                                    width: showAnimation
                                      ? `${xpProgress.percentage}%`
                                      : "0%",
                                    transitionDelay: showAnimation
                                      ? "800ms"
                                      : "0ms",
                                  }}
                                />
                              </div>
                              <div className="flex justify-between text-xs text-purple-600">
                                <span>{xpProgress.current} XP</span>
                                <span>{xpProgress.total} XP</span>
                              </div>
                            </div>

                            {/* Breakdown compatto dell'XP */}
                            {xpBreakdown.length > 0 && (
                              <details className="group cursor-pointer">
                                <summary className="text-sm text-purple-700 font-medium hover:text-purple-800 transition-colors">
                                  📊 Dettagli XP
                                </summary>
                                <div className="mt-2 space-y-1">
                                  {xpBreakdown.map((item, index) => {
                                    const xpMatch = item.match(/\+(\d+)\s*XP/);
                                    const xpValue = xpMatch ? xpMatch[1] : "";
                                    const description = item
                                      .replace(/\+\d+\s*XP$/, "")
                                      .replace(/:\s*$/, "");

                                    return (
                                      <div
                                        key={index}
                                        className="flex justify-between items-center text-xs py-1 px-2 rounded bg-white/50"
                                      >
                                        <span className="text-purple-700">
                                          {description}
                                        </span>
                                        {xpValue && (
                                          <span className="text-purple-600 font-bold">
                                            +{xpValue}
                                          </span>
                                        )}
                                      </div>
                                    );
                                  })}
                                </div>
                              </details>
                            )}

                            {/* Pulsante per annuncio con premio - XP Doppi */}
                            {Capacitor.isNativePlatform() &&
                              !rewardAdWatched && (
                                <div className="mt-4 pt-4 border-t border-purple-200">
                                  <ActionButton
                                    onClick={handleRewardedAd}
                                    disabled={isLoadingRewardAd}
                                    className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 disabled:from-gray-400 disabled:to-gray-500 text-white py-3 px-4 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200 flex items-center justify-center gap-3 font-bold text-sm"
                                  >
                                    {isLoadingRewardAd ? (
                                      <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                                        Caricamento...
                                      </>
                                    ) : (
                                      <>
                                        <div className="relative">🎬</div>
                                        Guarda annuncio per raddoppiare gli XP!
                                        <div className="bg-white/20 px-2 py-1 rounded-full text-xs">
                                          +{xpGained} XP
                                        </div>
                                      </>
                                    )}
                                  </ActionButton>
                                </div>
                              )}

                            {/* Messaggio XP raddoppiati */}
                            {rewardAdWatched && (
                              <div className="mt-4 p-3 bg-gradient-to-r from-green-100 to-emerald-100 rounded-xl border-2 border-green-300">
                                <div className="flex items-center justify-center gap-2">
                                  <div className="flex">
                                    <Star
                                      className="h-4 w-4 text-yellow-500"
                                      fill="currentColor"
                                    />
                                    <Star
                                      className="h-4 w-4 text-yellow-500 -ml-1"
                                      fill="currentColor"
                                    />
                                  </div>
                                  <span className="text-green-800 font-bold text-sm">
                                    🎉 XP Raddoppiati! Grazie per aver guardato
                                    l'annuncio!
                                  </span>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Bottoni azione compatti */}
            <div className="mt-4">
              {showAnimation && (
                <div className="animate-fade-in delay-200">
                  {/* Bottoni affiancati più piccoli */}
                  <div className="flex flex-row gap-2">
                    {/* Nuova Partita */}
                    <ActionButton
                      onClick={() => {
                        playSound("buttonClick");
                        onStartNewGame();
                      }}
                      className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white py-2.5 px-5 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 flex items-center justify-center gap-2 font-medium text-sm"
                      size="md"
                    >
                      <Play className="h-4 w-4" />
                      Nuova Partita
                    </ActionButton>

                    {/* Torna al Menu principale */}
                    <ActionButton
                      onClick={() => {
                        playSound("buttonClick");
                        onReturnToMenu();

                        // 📝 CONTROLLO RECENSIONI - Mostra richiesta nativa se appropriato
                        setTimeout(async () => {
                          const shouldShow = shouldShowReviewRequest();
                          console.log(
                            "🎯 Controllo recensione da GameOver, risultato:",
                            shouldShow
                          );

                          if (shouldShow) {
                            console.log(
                              "⭐ Mostrando richiesta recensione nativa Play Store da GameOver"
                            );
                            recordReviewRequestShown();
                            const result = await showNativeReviewModal();
                            console.log(
                              "📝 Risultato modale recensione da GameOver:",
                              result
                            );
                          } else {
                            console.log(
                              "📝 Recensione non necessaria da GameOver"
                            );
                          }
                        }, 1000); // Delay per permettere la transizione
                      }}
                      className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-2.5 px-5 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 flex items-center justify-center gap-2 font-medium text-sm"
                      size="md"
                    >
                      <Home className="h-4 w-4" />
                      Home
                    </ActionButton>
                  </div>
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default GameOverModal;
