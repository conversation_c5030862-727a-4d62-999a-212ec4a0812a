import React, { useState, useRef, useEffect } from "react";
import { createPortal } from "react-dom";
import { env } from "../../config/environment";
// Leggi la configurazione per il Pass Premium
const PASS_PREMIUM = env.features.passPremium;
import { useNavigate } from "react-router-dom";
import { Play, ChevronLeft, ChevronRight } from "lucide-react";
import ActionButton from "@/components/ui/ActionButton";
import { useAudio } from "@/hooks/useAudio";
import { useAuth } from "@/context/auth-context";
import { useUserState } from "@/hooks/useUserState";
import { useToast } from "@/hooks/use-toast";
import { Capacitor } from "@capacitor/core";

// Import Swiper e componenti necessari
import { Swiper, SwiperSlide } from "swiper/react";
import { EffectCoverflow, Pagination } from "swiper/modules";
import type { SwiperRef } from "swiper/react";

// Import stili di Swiper
import "swiper/css";
import "swiper/css/effect-coverflow";
import "swiper/css/pagination";

interface NewMobileMenuProps {
  difficulty: "easy" | "medium" | "hard";
  setDifficulty: (difficulty: "easy" | "medium" | "hard") => void;
  victoryPoints: "21" | "31" | "41";
  setVictoryPoints: (points: "21" | "31" | "41") => void;
}

type GameMode = "easy" | "medium" | "hard" | "online";

const NewMobileMenu: React.FC<NewMobileMenuProps> = ({
  difficulty,
  setDifficulty,
  victoryPoints,
  setVictoryPoints,
}) => {
  const navigate = useNavigate();
  // 🎯 SEMPLIFICAZIONE: Usa SOLO il sistema di auth centralizzato
  const { isLoggedIn, user, session, isLoading: authLoading } = useAuth();
  const { userState } = useUserState();
  const { toast } = useToast();

  // Detect Android platform for z-index fixes
  const isAndroid = Capacitor.getPlatform() === "android";

  const [selectedMode, setSelectedMode] = useState<GameMode>(difficulty);
  const [lastSelectedMode, setLastSelectedMode] =
    useState<GameMode>(difficulty);
  const [showPointsDropdown, setShowPointsDropdown] = useState(false);

  // 🚀 OTTIENI dati dal sistema di stato globale persistente
  const gameModes: Array<{
    id: GameMode;
    label: string;
    color: string;
    bgColor: string;
    available: boolean;
    image: string;
  }> = [
    {
      id: "easy",
      label: "Principiante",
      color: "text-green-700",
      bgColor: "bg-green-100 border-green-300",
      available: true,
      image: "/images/modes/principiante.png",
    },
    {
      id: "medium",
      label: "Esperto",
      color: "text-yellow-700",
      bgColor: "bg-yellow-100 border-yellow-300",
      available: true,
      image: "/images/modes/esperto.png",
    },
    {
      id: "hard",
      label: "Maestro",
      color: "text-red-700",
      bgColor: "bg-red-100 border-red-300",
      available: true,
      image: "/images/modes/maestro.png",
    },
    {
      id: "online",
      label: "Multiplayer",
      color: "text-blue-700",
      bgColor: "bg-blue-100 border-blue-300",
      available: false,
      image: "/images/modes/multi.png",
    },
  ];
  const currentIndex = gameModes.findIndex((mode) => mode.id === selectedMode);
  const swiperRef = useRef<SwiperRef>(null);
  const { playSound, playMusic, getCurrentTrackName } = useAudio();

  // State e ref per gestire il debouncing del suono
  const soundTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastSlideIndexRef = useRef<number>(currentIndex);

  // Debug: monitora i cambiamenti delle props
  useEffect(() => {
    console.log("🎯 Props victoryPoints aggiornate:", victoryPoints);
  }, [victoryPoints]);

  // Start background music when component mounts
  useEffect(() => {
    // Piccolo delay per permettere al componente di stabilizzarsi
    const musicTimer = setTimeout(() => {
      const currentTrack = getCurrentTrackName();

      // Avvia la musica solo se non c'è nessuna traccia in riproduzione
      if (!currentTrack) {
        playMusic("menu");
      }
    }, 100);

    return () => {
      clearTimeout(musicTimer);
    };
  }, [playMusic, getCurrentTrackName]);

  // Funzione per gestire il cambio di slide con debouncing del suono ottimizzato
  const handleSlideChange = (swiper) => {
    // Otteniamo l'indice reale considerando il loop
    const realIndex = swiper.realIndex;
    const newMode = gameModes[realIndex];

    // Evita re-render inutili
    if (newMode.id === selectedMode) return;

    // Aggiorna immediatamente lo stato visivo
    setSelectedMode(newMode.id);
    setLastSelectedMode(newMode.id);

    if (newMode.available && newMode.id !== "online") {
      setDifficulty(newMode.id as "easy" | "medium" | "hard");
    }

    // Gestione del suono con debouncing ottimizzato
    // Cancella il timeout precedente se esiste
    if (soundTimeoutRef.current) {
      clearTimeout(soundTimeoutRef.current);
    }

    // Imposta un nuovo timeout per riprodurre il suono solo dopo che l'utente ha smesso di trascinare
    soundTimeoutRef.current = setTimeout(() => {
      // Riproduci il suono solo se l'indice è effettivamente cambiato
      if (realIndex !== lastSlideIndexRef.current) {
        playSound("menuOpen");
        lastSlideIndexRef.current = realIndex;
      }
    }, 100); // Ridotto da 150ms a 100ms per migliore responsività
  };

  // Cleanup del timeout quando il componente viene unmountato
  useEffect(() => {
    return () => {
      if (soundTimeoutRef.current) {
        clearTimeout(soundTimeoutRef.current);
      }
    };
  }, []);

  // Chiudi dropdown quando si clicca fuori
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showPointsDropdown) {
        const target = event.target as HTMLElement;
        if (!target.closest(".points-dropdown-container")) {
          setShowPointsDropdown(false);
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showPointsDropdown]);
  const handleStartGame = () => {
    if (selectedMode === "online") return; // Disabled

    playSound("buttonClick");
    navigate("/game", {
      state: {
        difficulty: selectedMode as "easy" | "medium" | "hard",
        isOnline: false,
        victoryPoints: parseInt(victoryPoints),
      },
    });
  };
  const handleShopClick = () => {
    playSound("buttonClick");
    navigate("/shop");
  };

  const handleRulesClick = () => {
    playSound("buttonClick");
    navigate("/rules");
  };

  // Funzione stabile, sempre aggiornata, per selezione punti
  const handleVictoryPointsChange = (points: "21" | "31" | "41") => {
    console.log("🎯 Cambiando punti vittoria da", victoryPoints, "a", points);
    playSound("buttonClick");
    setVictoryPoints(points);
    setShowPointsDropdown(false);
  };

  const togglePointsDropdown = () => {
    console.log("🎯 Toggle dropdown, stato attuale:", showPointsDropdown);
    playSound("menuOpen");
    setShowPointsDropdown(!showPointsDropdown);
  };

  const currentMode = gameModes[currentIndex];

  // Calcola l'altezza corretta considerando footer
  const footerHeight = 70; // Altezza del footer
  const totalBottomSpace = footerHeight;

  return (
    <div
      className="h-screen max-h-screen flex flex-col relative overflow-hidden"
      style={{ height: `calc(100vh - ${totalBottomSpace}px)` }}
    >
      {/* Titolo fluttuante centrato con bottoni ai lati */}
      <div className="w-full flex flex-row items-center justify-center pt-6 pb-2 z-40 select-none">
        {/* Bottone premium a sinistra o placeholder */}
        <div
          className="flex-shrink-0 h-full flex items-start justify-start"
          style={{
            width: 80,
            minHeight: 80,
            display: "flex",
            alignItems: "flex-start",
            justifyContent: "flex-start",
          }}
        >
          {PASS_PREMIUM ? (
            <ActionButton
              onClick={handleShopClick}
              className="flex flex-col items-center pl-3 bg-gradient-to-r from-purple-700 to-pink-500 hover:from-pink-600 hover:to-purple-700 hover:scale-110 rounded-r-lg rounded-l-none transition-all duration-200 shadow-lg border-l-0 border-t border-b border-r border-white/30 min-h-[72px] min-w-[64px]"
              style={{ borderTopLeftRadius: 0, borderBottomLeftRadius: 0 }}
            >
              <img
                src="/images/icons/premium-crown 100x100.png"
                alt="Premium Crown"
                className="h-10 w-10 object-contain drop-shadow-lg transition-transform duration-200"
              />
              <span
                className="text-xs font-bold text-white whitespace-nowrap block -mt-1"
                style={{
                  fontFamily: "'DynaPuff', cursive",
                  fontWeight: 600,
                }}
              >
                Premium
              </span>
            </ActionButton>
          ) : (
            <div style={{ width: 56, height: 56 }} />
          )}
        </div>
        {/* Logo centrale */}
        <div className="flex-1 flex justify-center pointer-events-none select-none mt-2">
          <img
            src="/images/logos/marafone-scritta.png"
            alt="Marafone Romagnolo"
            className="max-h-24 md:max-h-28 w-auto drop-shadow-lg"
            style={{ objectFit: "contain" }}
            draggable={false}
          />
        </div>
        {/* Bottone regole a destra */}
        <div
          className="flex-shrink-0 h-full flex items-start justify-end"
          style={{
            width: 80,
            minHeight: 80,
            display: "flex",
            alignItems: "flex-start",
            justifyContent: "flex-end",
          }}
        >
          <ActionButton
            onClick={handleRulesClick}
            className="flex flex-col items-center pr-3 bg-gradient-to-r from-romagna-wood to-romagna-terracotta hover:from-romagna-terracotta hover:to-romagna-wood hover:scale-110 rounded-l-lg rounded-r-none transition-all duration-200 shadow-lg border-r-0 border-t border-b border-l border-white/30 min-h-[72px] min-w-[64px]"
            style={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}
          >
            <img
              src="/images/icons/rules 100x100.png"
              alt="Regole"
              className="h-10 w-10 object-contain drop-shadow-lg transition-transform duration-200"
            />
            <span
              className="text-xs font-bold text-white whitespace-nowrap block -mt-1"
              style={{
                fontFamily: "'DynaPuff', cursive",
                fontWeight: 600,
              }}
            >
              Regole
            </span>
          </ActionButton>
        </div>
      </div>
      {/* Main content centered, carosello più in basso */}
      <div
        className="flex-1 flex flex-col items-center justify-center px-0 w-full overflow-hidden max-w-full"
        style={{ marginTop: 24, minHeight: 0 }}
      >
        {/* Game Mode Carousel with Swiper */}
        <div className="w-full mb-6 mode-carousel-container transition-all duration-500 relative flex-1 max-h-[400px] overflow-hidden">
          <Swiper
            ref={swiperRef}
            effect={"coverflow"}
            grabCursor={true}
            centeredSlides={true}
            slidesPerView={2.5}
            spaceBetween={10}
            loop={true}
            touchStartPreventDefault={false}
            touchStartForcePreventDefault={false}
            allowTouchMove={true}
            simulateTouch={true}
            freeMode={false}
            coverflowEffect={{
              rotate: 5,
              stretch: 0,
              depth: 100,
              modifier: 2,
              slideShadows: false,
            }}
            initialSlide={currentIndex}
            modules={[EffectCoverflow, Pagination]}
            onSlideChange={handleSlideChange}
            className="w-full h-[250px] overflow-hidden max-w-full"
            style={{ maxWidth: "100vw", overflow: "hidden" }}
            aria-label="Selezione modalità di gioco"
            role="region"
          >
            {gameModes.map((mode) => (
              <SwiperSlide
                key={mode.id}
                className="flex flex-col items-center justify-center"
              >
                <div
                  className={`relative mt-8 mb-1 flex flex-col items-center transform ${
                    selectedMode === mode.id
                      ? "scale-[1.8] z-10 mb-8"
                      : "scale-80 opacity-60"
                  }`}
                  style={{
                    transition:
                      "transform 0.3s ease-in-out, opacity 0.3s ease-in-out",
                  }}
                >
                  <div
                    // onClick={
                    //   selectedMode === mode.id ? handleStartGame : undefined
                    // }
                    className={`relative h-32 w-32 group ${
                      selectedMode === mode.id && mode.available
                        ? "cursor-pointer hover:scale-110 active:scale-95"
                        : selectedMode === mode.id && !mode.available
                        ? "cursor-not-allowed"
                        : "cursor-default"
                    }`}
                    style={{
                      transition: "transform 0.2s ease-in-out",
                      pointerEvents: selectedMode === mode.id ? "auto" : "none",
                    }}
                    aria-label={`${mode.label} - ${
                      mode.available ? "Disponibile" : "Non disponibile"
                    }`}
                    role="button"
                    tabIndex={selectedMode === mode.id ? 0 : -1}
                  >
                    <img
                      src={mode.image}
                      alt={mode.label}
                      className={`w-full h-full object-contain rounded-lg ${
                        selectedMode === mode.id ? "animate-selected-pulse" : ""
                      }`}
                      style={{
                        transition: "all 0.3s ease-in-out",
                        userSelect: "none",
                        pointerEvents: "none",
                      }}
                    />

                    {!mode.available && (
                      <div className="absolute inset-0 bg-gray-500/70 rounded-lg flex items-center justify-center backdrop-blur-sm">
                        <div className="text-center">
                          <span className="text-white text-xs font-bold block">
                            Presto
                          </span>
                          <span className="text-white/80 text-[10px]">
                            Disponibile
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
          {/* Pulsanti di navigazione laterali */}
          <button
            onClick={() => swiperRef.current?.swiper.slidePrev()}
            className="absolute left-2 top-1/2 -translate-y-1/2 p-2 bg-white/90 hover:bg-white rounded-full border border-gray-200 shadow-lg backdrop-blur-sm transition-all duration-300 z-30 hover:scale-110 active:scale-95"
            aria-label="Modalità precedente"
            type="button"
          >
            <ChevronLeft className="h-5 w-5 text-gray-700" />
          </button>
          <button
            onClick={() => swiperRef.current?.swiper.slideNext()}
            className="absolute right-2 top-1/2 -translate-y-1/2 p-2 bg-white/90 hover:bg-white rounded-full border border-gray-200 shadow-lg backdrop-blur-sm transition-all duration-300 z-30 hover:scale-110 active:scale-95"
            aria-label="Modalità successiva"
            type="button"
          >
            <ChevronRight className="h-5 w-5 text-gray-700" />
          </button>
          {/* Play Button */}
          <div className="text-center">
            <ActionButton
              onClick={handleStartGame}
              disabled={!currentMode.available}
              className={`px-8 py-4 text-xl font-bold rounded-2xl shadow-xl transition-all duration-500 transform ${
                currentMode.available
                  ? "bg-gradient-to-r from-romagna-rust to-romagna-terracotta hover:from-romagna-terracotta hover:to-romagna-rust text-white hover:scale-110 active:scale-95 hover:shadow-2xl"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed opacity-50"
              }`}
            >
              <Play className="h-6 w-6 mr-2 inline animate-pulse" />
              {currentMode.available ? "Gioca!" : "Presto Disponibile"}
            </ActionButton>
            {/* Selettore punti vittoria compatto */}
            {currentMode.available && (
              <div className="mt-3 flex flex-col items-center space-y-2">
                <div className="flex flex-row items-center justify-center text-s text-gray-600">
                  Sfida l'
                  <span className="font-semibold text-blue-600 mr-1">
                    AI
                  </span>{" "}
                  fino a
                  <div
                    className={`relative points-dropdown-container ml-1 mr-1 ${
                      isAndroid ? "z-[10000]" : "z-[1001]"
                    }`}
                  >
                    {/* Bottone principale compatto, più piccolo */}
                    <button
                      onClick={togglePointsDropdown}
                      className="flex items-center justify-center bg-gradient-to-r from-amber-500 to-orange-500 text-white font-bold text-base px-2 py-0.5 rounded-md shadow-md hover:shadow-lg hover:scale-105 transition-all duration-200 min-w-[36px] h-7"
                      aria-label="Seleziona punti vittoria"
                      style={{ lineHeight: "1", fontSize: "1rem" }}
                    >
                      <span>{victoryPoints}</span>
                      <ChevronRight
                        className={`ml-1 h-3 w-3 transition-transform duration-200 ${
                          showPointsDropdown ? "rotate-90" : "rotate-0"
                        }`}
                      />
                    </button>
                    {/* Dropdown opzioni - posizionato sotto il bottone, opzioni in riga */}
                    {showPointsDropdown && (
                      <div
                        className="absolute flex flex-row bg-white/95 backdrop-blur-sm rounded-lg shadow-xl border border-gray-200 p-1"
                        style={{
                          minWidth: "auto",
                          bottom: "100%",
                          left: "50%",
                          transform: "translateX(-50%)",
                          marginBottom: "4px",
                          zIndex: isAndroid ? 99999 : 10000,
                        }}
                      >
                        {["21", "31", "41"].map((points) => (
                          <button
                            key={points}
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleVictoryPointsChange(
                                points as "21" | "31" | "41"
                              );
                            }}
                            className={`mx-1 px-3 py-1 rounded-md font-bold text-base transition-all duration-150 ${
                              victoryPoints === points
                                ? "bg-gradient-to-r from-amber-500 to-orange-500 text-white"
                                : "text-gray-700 hover:bg-gray-100"
                            }`}
                            style={{ fontSize: "1rem", minWidth: "36px" }}
                          >
                            {points}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>{" "}
                  <div className="text-s text-gray-500 font-medium mt-1">
                    punti
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      {/* Banner scorrevole */}
      {/* Decorative elements */}
      <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-romagna-wood/10 to-transparent pointer-events-none" />
      <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
        <div className="w-full h-full romagna-scratches rounded-full transform rotate-12" />
      </div>
      <div className="absolute bottom-16 left-4 w-24 h-24 opacity-10">
        <div className="w-full h-full romagna-scratches rounded-full transform -rotate-6" />
      </div>
    </div>
  );
};

export default NewMobileMenu;
